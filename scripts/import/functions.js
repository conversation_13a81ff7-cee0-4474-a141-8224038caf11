import _ from 'lodash';

import Article from '#modules/articles/models/Article.js';
import ArticleSite from '#modules/articles/models/ArticleSite.js';
import Category from '#modules/categories/models/Category.js';
import Person from '#modules/persons/models/Person.js';
import Channel from '#modules/media-library/models/Channel.js';
import Show from '#modules/media-library/models/Show.js';
import Season from '#modules/media-library/models/Season.js';
import Episode from '#modules/media-library/models/Episode.js';
import Video from '#modules/media-library/models/Video.js';
import VideoCategory from '#modules/media-library/models/VideoCategory.js';

import { saveRemoteFile } from '#modules/documents/controllers/documentController.js';
import { saveRemoteImage } from '#modules/images/controllers/imageController.js';

import jetstream from '#utils/jetstream.js';
import youtube from '#utils/youtube.js';
import Logger from '#utils/logger.js';
import { slugify } from '#utils/strings.js';

import { htmlToTiptap } from './tiptap/parse.js';

// TYPO3 tables
const ARTICLES_TABLE = 'tx_amsarticles_articles';
const SLUGS_TABLE = 'tx_realurl_uniqalias';
const SHOWS_TABLE = 'tx_amsmedialibrary_shows';
const EPISODES_TABLE = 'tx_amsmedialibrary_episodes';
const VIDEOS_TABLE = 'tx_amsmedialibrary_videos';

// Get the slug for the resource
const getSlug = async function ({
  ky,
  resourceId = 0,
  tablename = ARTICLES_TABLE,
} = {}) {
  const data = await ky
    .get('', {
      searchParams: {
        type: 2000,
        table: SLUGS_TABLE,
        sorting: 'uid DESC',
        filter: {
          tablename: { operator: '=', value: tablename },
          CONDITION: 'AND',
          value_id: { operator: '=', value: resourceId },
        },
      },
    })
    .json();

  return data[0] ? data[0].value_alias : '';
};

// Compose image location
const composeLocation = (file) => {
  const { location_city, location_region, location_country } = file;

  return `${location_city || ''}${
    location_region ? `, ${location_region}` : ''
  }${location_country ? `, ${location_country}` : ''}`;
};

// Compose image caption
const composeCaption = (filename = '') =>
  decodeURIComponent(filename)
    .replace(/[^a-zA-Z0-9]|CroppedforPosting?/g, ' ')
    .replace(/\s{2,}/g, ' ')
    .replace(/^\s+/, '')
    .replace(/\s+$/, '');

const importFile = async function (publicUrl, entity) {
  try {
    // Upload the file to the CDN
    const docFile = await saveRemoteFile(publicUrl, entity);

    Logger.info('Imported file from', publicUrl);

    return docFile;
  } catch (error) {
    // Log the error
    Logger.error(`Error when trying to obtain file from ${publicUrl}`, error);
  }
};

const importImage = async function (publicUrl, entity) {
  try {
    // Upload the file to the CDN
    const imageFile = await saveRemoteImage(publicUrl, entity);

    Logger.info('Imported image from', publicUrl);

    return imageFile;
  } catch (error) {
    // Log the error
    Logger.error(`Error when trying to obtain image from ${publicUrl}`, error);
  }
};
// Function to add asset info
const addAssetInfo = async function (entity, nodes = []) {
  for (const node of nodes) {
    // Image
    if (node.type === 'image') {
      if (_.isEmpty(node.attrs.url)) {
        // Log error
        Logger.error('Image node without URL:', node.attrs);

        // Add this to the node so we know there was a problem
        node.attrs = {
          caption: 'Import error',
          file: null,
        };
      } else {
        const imageFile = await importImage(node.attrs.url, entity);

        if (imageFile) {
          // Set the new node attributes
          node.attrs = {
            caption: !_.isEmpty(node.attrs.caption)
              ? node.attrs.caption
              : composeCaption(imageFile.originalFilename),
            file: imageFile,
          };
        } else {
          // Add this to the node so we know there was a problem
          node.attrs = {
            caption: 'Import error',
            file: null,
          };
        }
      }
    }

    // Do it recursively with its children
    if (Array.isArray(node.content)) await addAssetInfo(node.content);
  }
};

const importShows = async function (ky, channel, entity, lastSync = 0) {
  try {
    Logger.info('Importing shows');

    const params = {
      type: 2000,
      table: SHOWS_TABLE,
      sorting: 'tstamp ASC',
      related: 1,
      filter: {
        'fk_channel': { operator: '=', value: channel.importIDs[0] },
        'CONDITION': 'AND',
        'tx_amsbroadcast_vod_hidden': { operator: '=', value: false },
        'CONDITION-1': 'AND',
        'tstamp': { operator: '>=', value: lastSync },
        // 'CONDITION-2': 'AND',
        // 'uid': {
        //   operator: '=',
        //   value: 8, // TODO: hope sabbath school
        // },
      },
    };

    const showCount = await ky
      .get('', {
        searchParams: { ...params, count: 1 },
      })
      .json();

    let importCount = 0;
    const limit = 10;
    let offset = 0;

    const { numberOfRecords } = showCount;

    if (numberOfRecords > 0) {
      Logger.info(`Attempting to import ${numberOfRecords} shows`);

      while (offset < numberOfRecords) {
        Logger.info(
          `Importing shows ${offset + 1} to ${Math.min(
            limit + offset,
            numberOfRecords
          )} of ${numberOfRecords}\n`
        );

        const shows = await ky
          .get('', {
            searchParams: { ...params, limit: `${offset},${limit}` },
          })
          .json();

        for (const show of shows) {
          if (_.isEmpty(show.title)) {
            Logger.warning(
              `Not importing show with UID ${show.uid} because its title is empty!`
            );
            continue;
          }

          const existingShow = await Show.findOne({
            channel,
            importIDs: {
              $elemMatch: { type: 'typo3', recordID: `${show.uid}` },
            },
          });

          if (existingShow) {
            Logger.info(
              `Updating show with external UID ${show.uid} and local ID ${existingShow.id}`
            );
          } else {
            Logger.info(`Importing show with UID ${show.uid}`);
          }

          // Fixing show documents and season document titles
          // ---------------------------------
          // if (existingShow) {
          //   // Loop through seasons
          //   for (const existingSeasonId of existingShow.seasons) {
          //     const existingSeason = await Season.findById(existingSeasonId);
          //     if (existingSeason) {
          //       // Loop through season documents
          //       const seasonDocuments = [];
          //       let seasonUpdated = false;
          //       for (const existingSeasonDocument of existingSeason.documents) {
          //         const typo3season = show.fk_seasons.find((season) =>
          //           areEqualIDs(season.uid, existingSeason.importID)
          //         );
          //         if (typo3season) {
          //           const typo3document = typo3season.documents.find(
          //             (doc) =>
          //               doc.file.name ===
          //               `${existingSeasonDocument.file.originalFilename}${existingSeasonDocument.file.extension}`
          //           );
          //           if (
          //             typo3document &&
          //             typo3document.reference.title &&
          //             typo3document.reference.title !==
          //               existingSeasonDocument.title
          //           ) {
          //             existingSeasonDocument.title =
          //               typo3document.reference.title;
          //             seasonUpdated = true;
          //           }
          //         }
          //         seasonDocuments.push(existingSeasonDocument);
          //       }
          //       if (seasonUpdated)
          //         await existingSeason.updateOne({
          //           documents: seasonDocuments,
          //         });
          //     }
          //   }

          //   // Loop through documents
          //   const documents = [];
          //   let showUpdated = false;
          //   for (const existingDocument of existingShow.documents) {
          //     // find TYPO3 document by comparing original file name
          //     const typo3document = show.documents.find(
          //       (doc) =>
          //         doc.file.name ===
          //         `${existingDocument.file.originalFilename}${existingDocument.file.extension}`
          //     );
          //     if (
          //       typo3document &&
          //       typo3document.reference.title &&
          //       typo3document.reference.title !== existingDocument.title
          //     ) {
          //       existingDocument.title = typo3document.reference.title;
          //       showUpdated = true;
          //     }
          //     documents.push(existingDocument);
          //   }
          //   // Update the existing episode
          //   if (showUpdated) await existingShow.updateOne({ documents });
          // }
          // continue;
          // ---------------------------------

          const {
            crdate, // -> createdAt
            hidden, // -> enabled
            deleted, // -> deleted
            title, // -> title
            short_description, // -> abstract
            long_description, // -> body
            contact_email, // -> contactEmail
            documents, // -> documents
            image, // -> images.default
            background_image, // -> backgroundImages.default
            fk_alternative_images, // -> images.podcast, backgroundImages.phone/tablet/tv
            fk_categories, // -> categories
            fk_hosts, // -> hosts
            // fk_import_ids, // -> importIDs
            fk_language, // -> language
            fk_links, // -> links
            fk_podcasts, // -> podcasts
            fk_seasons, // -> seasons
            fk_tags, // -> tags
            fk_video_categories, // -> videoCategories
            tx_amsbroadcast_vod_start_time, // -> videoOnDemandStartsAt
            tx_amsbroadcast_vod_end_time, // -> videoOnDemandEndsAt
            tx_amsbroadcast_vod_hidden, // -> videoOnDemand
            background_color, // -> colors.page.background
            title_color, // -> colors.page.title
            text_color, // -> colors.page.text
            card_background_color, // -> colors.box.background
            episodes_downloadable, // -> episodesDownloadable
            rss_categories, // -> rss.categories
            rss_subcategories, // -> rss.subcategories
            videos_title, // -> videosTitle
          } = show;

          const baseShow = {
            channel,
            enabled: !hidden,
            deleted,
            title,
            abstract: short_description,
            contactEmail: contact_email,
            videoOnDemandStartsAt: tx_amsbroadcast_vod_start_time,
            videoOnDemandEndsAt: tx_amsbroadcast_vod_end_time,
            videoOnDemand: !tx_amsbroadcast_vod_hidden,
            episodesDownloadable: episodes_downloadable,
            videosTitle: videos_title,
            videoCategories: [],
            language: fk_language.ietfoftitle
              ? fk_language.ietfoftitle.split('-')[0]
              : channel.mainLanguage,
            links: fk_links
              .filter((l) => l.link_type === 0 && !_.isEmpty(l.link))
              .map((l) => ({
                enabled: !l.hidden && !l.deleted,
                url: l.link,
                title: l.title || l.link,
              })),
            colors: {
              page: {
                background: background_color,
                title: title_color,
                text: text_color,
              },
              box: {
                background: card_background_color,
              },
            },
            body: {
              type: 'doc',
              content: [],
            },
            images: { default: null, banner: null, podcast: null },
            backgroundImages: {
              default: null,
              phone: null,
              tablet: null,
              tv: null,
            },
            documents: [],
            hosts: [],
            tags: _.uniq(fk_tags.map((tag) => tag.title)),
            categories: [],
            seasons: [],
            podcasts: fk_podcasts.map((p) => ({
              enabled: !p.hidden && !p.deleted,
              title: p.title,
              url: p.url,
            })),
            scheduleIDs: [
              {
                enabled: true,
                scheduleID: slugify(title),
              },
            ],
            importIDs: [
              {
                type: 'typo3',
                recordID: `${show.uid}`,
              },
            ],
          };

          // Deserialize the html
          const body = htmlToTiptap(long_description);

          if (body) {
            // If there are assets embedded, add them to storage and save their info
            await addAssetInfo(entity, body.content);
            baseShow.body = body;
          }

          // Images
          if (image && image.publicUrl !== '') {
            const imageFile = await importImage(image.publicUrl, entity);

            if (imageFile) {
              // Set the new image data
              baseShow.images.default = imageFile;
            }
          }

          if (background_image && background_image.publicUrl !== '') {
            const imageFile = await importImage(
              background_image.publicUrl,
              entity
            );

            if (imageFile) {
              // Set the new image data
              baseShow.backgroundImages.default = imageFile;
            }
          }

          for (const altImage of fk_alternative_images) {
            const device = altImage.fk_delivery_channels[0];

            if (device) {
              switch (device.name) {
                case 'Podcast':
                  if (altImage.image && altImage.image.publicUrl !== '') {
                    const imageFile = await importImage(
                      altImage.image.publicUrl,
                      entity
                    );

                    if (imageFile) {
                      // Set the new image data
                      baseShow.images.podcast = imageFile;
                    }
                  }
                  break;

                case 'Apple iPhone':
                  if (
                    !baseShow.backgroundImages.phone &&
                    altImage.background_image &&
                    altImage.background_image.publicUrl !== ''
                  ) {
                    const imageFile = await importImage(
                      altImage.background_image.publicUrl,
                      entity
                    );

                    if (imageFile) {
                      // Set the new image data
                      baseShow.backgroundImages.phone = imageFile;
                    }
                  }
                  break;

                case 'Android Phone':
                  if (
                    !baseShow.backgroundImages.phone &&
                    altImage.background_image &&
                    altImage.background_image.publicUrl !== ''
                  ) {
                    const imageFile = await importImage(
                      altImage.background_image.publicUrl,
                      entity
                    );

                    if (imageFile) {
                      // Set the new image data
                      baseShow.backgroundImages.phone = imageFile;
                    }
                  }
                  break;

                case 'Apple iPad':
                  if (
                    !baseShow.backgroundImages.tablet &&
                    altImage.background_image &&
                    altImage.background_image.publicUrl !== ''
                  ) {
                    const imageFile = await importImage(
                      altImage.background_image.publicUrl,
                      entity
                    );

                    if (imageFile) {
                      // Set the new image data
                      baseShow.backgroundImages.tablet = imageFile;
                    }
                  }
                  break;

                case 'Android Tablet':
                  if (
                    !baseShow.backgroundImages.tablet &&
                    altImage.background_image &&
                    altImage.background_image.publicUrl !== ''
                  ) {
                    const imageFile = await importImage(
                      altImage.background_image.publicUrl,
                      entity
                    );

                    if (imageFile) {
                      // Set the new image data
                      baseShow.backgroundImages.tablet = imageFile;
                    }
                  }
                  break;

                case 'Roku':
                  if (
                    !baseShow.backgroundImages.tv &&
                    altImage.background_image &&
                    altImage.background_image.publicUrl !== ''
                  ) {
                    const imageFile = await importImage(
                      altImage.background_image.publicUrl,
                      entity
                    );

                    if (imageFile) {
                      // Set the new image data
                      baseShow.backgroundImages.tv = imageFile;
                    }
                  }
                  break;

                case 'SmartTV':
                  if (
                    !baseShow.backgroundImages.tv &&
                    altImage.background_image &&
                    altImage.background_image.publicUrl !== ''
                  ) {
                    const imageFile = await importImage(
                      altImage.background_image.publicUrl,
                      entity
                    );

                    if (imageFile) {
                      // Set the new image data
                      baseShow.backgroundImages.tv = imageFile;
                    }
                  }
                  break;

                default:
                  break;
              }
            }
          }

          // Documents
          for (const doc of documents) {
            if (doc.publicUrl) {
              const docFile = await importFile(doc.publicUrl, entity);

              if (docFile) {
                const { file, reference } = doc;

                baseShow.documents.push({
                  file: docFile,
                  title: !_.isEmpty(reference.title)
                    ? reference.title
                    : !_.isEmpty(file.title)
                      ? file.title
                      : composeCaption(docFile.originalFilename),
                  description: file.description,
                });
              }
            }
          }

          // Function to generate seasons (it's called at the end, once the show is created)
          const generateSeasons = async (_show) => {
            const seasons = [];

            for (const season of fk_seasons) {
              const baseSeason = {
                channel,
                show: _show,
                importID: `${season.uid}`,
                enabled: !season.hidden && !season.deleted,
                number: season.number,
                title: season.title,
                groupName: season.group_name,
                documents: [],
                links: season.fk_links
                  .filter((l) => l.link_type === 0 && !_.isEmpty(l.link))
                  .map((l) => ({
                    enabled: !l.hidden && !l.deleted,
                    url: l.link,
                    title: l.title || l.link,
                  })),
              };

              for (const doc of season.documents) {
                if (doc.publicUrl) {
                  const docFile = await importFile(doc.publicUrl, entity);

                  if (docFile) {
                    const { file, reference } = doc;

                    baseSeason.documents.push({
                      file: docFile,
                      title: !_.isEmpty(reference.title)
                        ? reference.title
                        : !_.isEmpty(file.title)
                          ? file.title
                          : composeCaption(docFile.originalFilename),
                      description: file.description,
                    });
                  }
                }
              }

              const existingSeason = await Season.findOne({
                show: _show,
                importID: `${season.uid}`,
              });

              if (existingSeason) {
                await existingSeason.updateOne(baseSeason);

                Logger.success(
                  `Successfully updated season. ID: ${existingSeason.id}`
                );

                seasons.push(existingSeason._id);
              } else {
                // Generate season's slug
                let slug = slugify(
                  season.group_name
                    ? `${season.group_name} ${season.title}`
                    : season.title
                );

                // Ensure season has a valid slug within the show
                slug = await Season.getAvailableSlug(slug, _show.id);

                // Create the new season
                const newSeason = await Season.create({
                  ...baseSeason,
                  slug,
                  createdAt: season.crdate,
                  updatedAt: season.tstamp,
                });

                Logger.success(
                  `Successfully imported season. ID: ${newSeason.id}`
                );

                seasons.push(newSeason._id);
              }
            }

            return seasons;
          };

          // Function to generate video categories (it's called at the end, once the show is created)
          const generateVideoCategories = async (_show) => {
            const videoCategories = [];

            for (const videoCategory of fk_video_categories) {
              const baseVideoCategory = {
                show: _show,
                importID: `${videoCategory.uid}`,
                enabled: !videoCategory.hidden && !videoCategory.deleted,
                title: videoCategory.title,
              };

              const existingVideoCategory = await VideoCategory.findOne({
                show: _show,
                importID: `${videoCategory.uid}`,
              });

              if (existingVideoCategory) {
                await existingVideoCategory.updateOne(baseVideoCategory);

                Logger.success(
                  `Successfully updated video category. ID: ${existingVideoCategory.id}`
                );

                videoCategories.push(existingVideoCategory._id);
              } else {
                // Generate video categorie's slug
                let slug = slugify(videoCategory.title);

                // Ensure video category has a valid slug within the show
                slug = await VideoCategory.getAvailableSlug(slug, _show.id);

                // Create the new video category
                const newVideoCategory = await VideoCategory.create({
                  ...baseVideoCategory,
                  slug,
                  createdAt: videoCategory.crdate,
                  updatedAt: videoCategory.tstamp,
                });

                Logger.success(
                  `Successfully imported video category. ID: ${newVideoCategory.id}`
                );

                videoCategories.push(newVideoCategory._id);
              }
            }

            return videoCategories;
          };

          // Hosts
          if (
            !_.isEmpty(fk_hosts) &&
            !_.isEmpty(fk_hosts.tx_amsmedialibrary_persons)
          ) {
            for (const host of fk_hosts.tx_amsmedialibrary_persons) {
              const existingPerson = await Person.findOne({
                importIDs: {
                  $elemMatch: {
                    type: `typo3-channel-${channel.id}`,
                    recordID: `${host.uid}`,
                  },
                },
              });

              if (existingPerson) {
                // Update existing person's roles
                await existingPerson.updateOne({
                  roles: _.uniq([...existingPerson.roles, 'host']),
                });

                Logger.success(
                  `Successfully updated person. ID: ${existingPerson.id}`
                );

                baseShow.hosts = _.uniqBy(
                  [...baseShow.hosts, existingPerson.id],
                  (id) => id.toString()
                );
              } else {
                const basePerson = {
                  enabled: !host.hidden,
                  deleted: host.deleted,
                  gender: host.gender === 2 ? 'female' : 'male',
                  prefix: host.prefix,
                  firstName: host.first_name,
                  middleName: host.middle_name,
                  lastName: host.last_name,
                  suffix: host.suffix,
                  avatar: null,
                  image: null,
                  body: {
                    type: 'doc',
                    content: [],
                  },
                  roles: ['host'],
                  email: host.email,
                  channel,
                  importIDs: [
                    {
                      type: `typo3-channel-${channel.id}`,
                      recordID: `${host.uid}`,
                    },
                  ],
                };

                // Deserialize the html
                const hostBody = htmlToTiptap(host.description);

                if (hostBody) {
                  // If there are assets embedded, add them to storage and save their info
                  await addAssetInfo(entity, hostBody.content);
                  basePerson.body = hostBody;
                }

                if (host.thumbnail && host.thumbnail.publicUrl !== '') {
                  const imageFile = await importImage(
                    host.thumbnail.publicUrl,
                    entity
                  );

                  if (imageFile) {
                    // Set the new image data
                    basePerson.avatar = imageFile;
                  }
                }

                if (host.image && host.image.publicUrl !== '') {
                  const imageFile = await importImage(
                    host.image.publicUrl,
                    entity
                  );

                  if (imageFile) {
                    // Set the new image data
                    basePerson.image = imageFile;
                  }
                }

                const fullName =
                  `${basePerson.prefix} ${basePerson.firstName} ${basePerson.middleName} ${basePerson.lastName} ${basePerson.suffix}`.replace(
                    /undefined/g,
                    ''
                  );

                // Ensure person has a valid slug within its siblings (or create one from its name)
                const slug = await Person.getAvailableSlug(
                  slugify(fullName),
                  channel
                );

                // Create the new person
                const newPerson = await Person.create({
                  ...basePerson,
                  slug,
                  createdAt: host.crdate,
                  updatedAt: host.tstamp,
                });

                Logger.success(
                  `Successfully imported person. ID: ${newPerson.id}`
                );

                baseShow.hosts = _.uniqBy(
                  [...baseShow.hosts, newPerson.id],
                  (id) => id.toString()
                );
              }
            }
          }

          // Add the categories
          for (const category of fk_categories) {
            // First try to find it for the specific entity
            let existingCategory = await Category.findOne({
              importIDs: `${category.uid}`,
              entity,
            });

            // Then, in the globals
            if (!existingCategory) {
              existingCategory = await Category.findOne({
                importIDs: `${category.uid}`,
                entity: null,
              });
            }

            // If the category exists, add it
            if (existingCategory) {
              baseShow.categories.push(existingCategory.id);
            }
          }

          // Set RSS feed categories
          const rssCategories = rss_categories
            .split(',')
            .filter((s) => s.trim())
            .map((s) => s.trim());

          const rssSubcategories = rss_subcategories
            .split(',')
            .filter((s) => s.trim())
            .map((s) => s.trim());

          if (existingShow) {
            // Add RSS feed
            baseShow.rss = {
              ...existingShow.rss,
              categories: rssCategories,
              subcategories: rssSubcategories,
            };

            // Add the seasons
            baseShow.seasons = await generateSeasons(existingShow);

            // Add the video categories
            baseShow.videoCategories =
              await generateVideoCategories(existingShow);

            // Keep poster image of existing show
            if (existingShow.images?.poster) {
              baseShow.images.poster = existingShow.images.poster;
            }

            // Update the existing show
            await existingShow.updateOne(baseShow);

            Logger.success(`Successfully updated show. ID: ${existingShow.id}`);
          } else {
            // Get the show's slug
            let slug = await getSlug({
              ky,
              resourceId: show.uid,
              tablename: SHOWS_TABLE,
            });

            // If slug is empty, generate it
            if (_.isEmpty(slug)) {
              slug = slugify(show.title);
            }

            // Ensure show has a valid slug within its siblings
            slug = await Show.getAvailableSlug(slug, channel);

            // Create the new show
            const newShow = await Show.create({
              ...baseShow,
              slug,
              createdAt: crdate,
              updatedAt: show.tstamp,
            });

            // Add RSS feed
            baseShow.rss = {
              categories: rssCategories,
              subcategories: rssSubcategories,
            };

            // Add the seasons
            newShow.seasons = await generateSeasons(newShow);

            // Update the seasons in the new show
            await newShow.save();

            Logger.success(`Successfully imported show. ID: ${newShow.id}`);
          }

          importCount += 1;
        }

        // Increase the offset
        offset += limit;
      }
    }

    if (importCount === 0) {
      Logger.info('No shows found to import');
    } else {
      Logger.info(`${importCount} shows imported`);
    }
  } catch (error) {
    Logger.error('Error when trying to import shows:', error);
  }
};

const importVideo = async function (
  ky,
  videoId,
  show,
  episode,
  entity,
  addRelated = true
) {
  const params = {
    type: 2000,
    table: VIDEOS_TABLE,
    related: 1,
    limit: 1,
    filter: {
      uid: {
        operator: '=',
        value: videoId,
      },
    },
  };

  const { data: videos } = await ky.get('', { params });

  const video = videos[0];

  if (!_.isEmpty(video) && !_.isEmpty(video.title)) {
    let existingVideo = await Video.findOne({
      show,
      importIDs: `${video.uid}`,
    });

    if (existingVideo) {
      Logger.info(
        `Updating video with external UID ${video.uid} and local ID ${existingVideo.id}`
      );
    } else {
      Logger.info(`Importing video with UID ${video.uid}`);
    }

    const baseVideo = {
      title: video.title,
      abstract: video.short_description,
      body: {
        type: 'doc',
        content: [],
      },
      image: {},
      videoCategories: [],
      tags: _.uniq(video.fk_tags.map((tag) => tag.title)),
      mediaLinks: {
        'youtube': null,
        'jetstream': null,
        'vimeo': null,
        'mp4-hd': null,
        'mp4-sd': null,
        'mp3': null,
        'hls': null,
        'bif': null,
      },
      show: show.id,
      episode: episode.id,
      relatedVideos: [],
      importIDs: [`${video.uid}`],
    };

    // Deserialize the html
    const body = htmlToTiptap(video.long_description);

    if (body) {
      // If there are assets embedded, add them to storage and save their info
      await addAssetInfo(entity, body.content);
      baseVideo.body = body;
    }

    // Image
    if (video.image && video.image.publicUrl !== '') {
      const imageFile = await importImage(video.image.publicUrl, entity);

      if (imageFile) {
        // Set the new image data
        baseVideo.image = imageFile;
      }
    }

    // Media Links
    if (!_.isEmpty(video.link)) {
      switch (video.fk_media_format.uid) {
        // MP3
        case 3:
          baseVideo.mediaLinks.mp3 = {
            link: video.link,
          };
          break;

        // MP4 HD
        case 5:
          baseVideo.mediaLinks['mp4-hd'] = {
            link: video.link,
          };
          break;

        // MP4
        case 2:
          // Only if there is no MP4 HD
          if (_.isEmpty(baseVideo.mediaLinks['mp4-hd'])) {
            baseVideo.mediaLinks['mp4-hd'] = {
              link: video.link,
            };
          }
          break;

        // MP4 Mobile
        case 6:
          baseVideo.mediaLinks['mp4-sd'] = {
            link: video.link,
          };
          break;

        // m3u8
        case 8:
          baseVideo.mediaLinks.hls = {
            link: video.link,
          };
          break;

        // BIF
        case 14:
          baseVideo.mediaLinks.bif = {
            link: video.link,
          };
          break;

        // Jetstream
        case 13: {
          const jetstreamVideo = await jetstream.getVideo(video.link);
          baseVideo.mediaLinks.jetstream =
            jetstream.getMediaLink(jetstreamVideo);
          break;
        }

        // YouTube
        case 7:
          baseVideo.mediaLinks.youtube = await youtube.getMediaLink(video.link);
          break;

        default:
          break;
      }
    }
    if (!_.isEmpty(video.link_2)) {
      switch (video.fk_media_format_2.uid) {
        // MP3
        case 3:
          baseVideo.mediaLinks.mp3 = {
            link: video.link_2,
          };
          break;

        // MP4 HD
        case 5:
          baseVideo.mediaLinks['mp4-hd'] = {
            link: video.link_2,
          };
          break;

        // MP4
        case 2:
          // Only if there is no MP4 HD
          if (_.isEmpty(baseVideo.mediaLinks['mp4-hd'])) {
            baseVideo.mediaLinks['mp4-hd'] = {
              link: video.link_2,
            };
          }
          break;

        // MP4 Mobile
        case 6:
          baseVideo.mediaLinks['mp4-sd'] = {
            link: video.link_2,
          };
          break;

        // m3u8
        case 8:
          baseVideo.mediaLinks.hls = {
            link: video.link_2,
          };
          break;

        // BIF
        case 14:
          baseVideo.mediaLinks.bif = {
            link: video.link_2,
          };
          break;

        // Jetstream
        case 13: {
          const jetstreamVideo = await jetstream.getVideo(video.link_2);
          baseVideo.mediaLinks.jetstream =
            jetstream.getMediaLink(jetstreamVideo);
          break;
        }

        // YouTube
        case 7:
          baseVideo.mediaLinks.youtube = await youtube.getMediaLink(
            video.link_2
          );
          break;

        default:
          break;
      }
    }
    if (!_.isEmpty(video.link_3)) {
      switch (video.fk_media_format_3.uid) {
        // MP3
        case 3:
          baseVideo.mediaLinks.mp3 = {
            link: video.link_3,
          };
          break;

        // MP4 HD
        case 5:
          baseVideo.mediaLinks['mp4-hd'] = {
            link: video.link_3,
          };
          break;

        // MP4
        case 2:
          // Only if there is no MP4 HD
          if (_.isEmpty(baseVideo.mediaLinks['mp4-hd'])) {
            baseVideo.mediaLinks['mp4-hd'] = {
              link: video.link_3,
            };
          }
          break;

        // MP4 Mobile
        case 6:
          baseVideo.mediaLinks['mp4-sd'] = {
            link: video.link_3,
          };
          break;

        // m3u8
        case 8:
          baseVideo.mediaLinks.hls = {
            link: video.link_3,
          };
          break;

        // BIF
        case 14:
          baseVideo.mediaLinks.bif = {
            link: video.link_3,
          };
          break;

        // Jetstream
        case 13: {
          const jetstreamVideo = await jetstream.getVideo(video.link_3);
          baseVideo.mediaLinks.jetstream =
            jetstream.getMediaLink(jetstreamVideo);
          break;
        }

        // YouTube
        case 7:
          baseVideo.mediaLinks.youtube = await youtube.getMediaLink(
            video.link_3
          );
          break;

        default:
          break;
      }
    }

    // Function to add video categories to videos
    const addVideoCategories = async (_show) => {
      const videoCategories = [];

      for (const videoCategory of video.fk_video_categories) {
        const existingVideoCategory = await VideoCategory.findOne({
          show: _show,
          importID: `${videoCategory.uid}`,
        });

        if (existingVideoCategory) {
          videoCategories.push(existingVideoCategory._id);
        }
      }

      return videoCategories;
    };

    // Add the video categories
    baseVideo.videoCategories = await addVideoCategories(show);

    if (existingVideo) {
      // Update the existing video
      await existingVideo.updateOne(baseVideo);

      Logger.success(`Successfully updated video. ID: ${existingVideo.id}`);
    } else {
      // Get the video's slug
      let slug = await getSlug({
        ky,
        resourceId: video.uid,
        tablename: VIDEOS_TABLE,
      });

      // If slug is empty empty, generate it
      if (_.isEmpty(slug)) {
        slug = slugify(video.title);
      }

      // Ensure video has a valid slug within its siblings
      slug = await Video.getAvailableSlug(slug, show);

      // Create the new video
      existingVideo = await Video.create({
        ...baseVideo,
        slug,
        createdAt: video.crdate,
        updatedAt: video.tstamp,
      });

      Logger.success(`Successfully imported video. ID: ${existingVideo.id}`);
    }

    if (
      addRelated &&
      !_.isEmpty(video.fk_related_videos) &&
      !_.isEmpty(video.fk_related_videos.tx_amsmedialibrary_videos)
    ) {
      Logger.info(`Adding related videos to video. ID: ${existingVideo.id}`);
      let relatedVideos = [];

      for (const relatedVideo of video.fk_related_videos
        .tx_amsmedialibrary_videos) {
        const relatedVideoId = await importVideo(
          ky,
          relatedVideo.uid,
          show,
          episode,
          entity,
          false
        );

        if (relatedVideoId) {
          relatedVideos.push(relatedVideoId);
        }
      }

      relatedVideos = _.uniqBy(relatedVideos, (id) => id.toString());

      await existingVideo.updateOne({ relatedVideos });
    }

    return existingVideo.id;
  }
  return null;
};

const importEpisodes = async function (ky, channel, entity, lastSync = 0) {
  try {
    Logger.info('Importing episodes');

    const params = {
      type: 2000,
      table: EPISODES_TABLE,
      sorting: 'tstamp ASC',
      related: 1,
      filter: {
        'fk_channel': {
          operator: '=',
          value: channel.importIDs[0],
        },
        'CONDITION': 'AND',
        'tx_amsbroadcast_vod_hidden': { operator: '=', value: false },
        'CONDITION-1': 'AND',
        'tstamp': { operator: '>=', value: lastSync },
        // 'CONDITION-2': 'AND',
        // fk_show: {
        //   operator: '=',
        //   value: 354,
        // },
        // 'CONDITION-2': 'AND',
        // 'uid': {
        //   operator: '=',
        //   value: 23919,
        // },
      },
    };

    const episodeCount = await ky
      .get('', {
        searchParams: { ...params, count: 1 },
      })
      .json();

    let importCount = 0;
    const limit = 10;
    let offset = 0;

    const { numberOfRecords } = episodeCount;

    if (numberOfRecords > 0) {
      Logger.info(`Attempting to import ${numberOfRecords} episodes`);

      while (offset < numberOfRecords) {
        Logger.info(
          `Importing episodes ${offset + 1} to ${Math.min(
            limit + offset,
            numberOfRecords
          )} of ${numberOfRecords}\n`
        );

        const episodes = await ky
          .get('', {
            searchParams: { ...params, limit: `${offset},${limit}` },
          })
          .json();

        for (const episode of episodes) {
          if (_.isEmpty(episode.title)) {
            Logger.warning(
              `Not importing episode with UID ${episode.uid} because its title is empty!`
            );
            continue;
          }

          const show = await Show.findOne({
            channel,
            importIDs: {
              $elemMatch: {
                type: 'typo3',
                recordID: `${
                  typeof episode.fk_show === 'object'
                    ? episode.fk_show.uid
                    : episode.fk_show
                }`,
              },
            },
          });

          if (!show) {
            Logger.warning(
              `Not importing episode with UID ${episode.uid} because the show is not imported`
            );
            continue;
          }

          let existingEpisode = await Episode.findOne({
            show,
            importIDs: {
              $elemMatch: { type: 'typo3', recordID: `${episode.uid}` },
            },
          });

          if (existingEpisode) {
            Logger.info(
              `Updating episode with external UID ${episode.uid} and local ID ${existingEpisode.id}`
            );
          } else {
            Logger.info(`Importing episode with UID ${episode.uid}`);
          }

          // Fixing episode document titles
          // ---------------------------------
          // if (existingEpisode) {
          //   const documents = [];
          //   let episodeUpdated = false;
          //   for (const existingDocument of existingEpisode.documents) {
          //     // find TYPO3 document by comparing original file name
          //     const typo3document = episode.documents.find(
          //       (doc) =>
          //         doc.file.name ===
          //         `${existingDocument.file.originalFilename}${existingDocument.file.extension}`
          //     );
          //     if (
          //       typo3document &&
          //       typo3document.reference.title &&
          //       typo3document.reference.title !== existingDocument.title
          //     ) {
          //       existingDocument.title = typo3document.reference.title;
          //       episodeUpdated = true;
          //     }
          //     documents.push(existingDocument);
          //   }
          //   // Update the existing episode
          //   if (episodeUpdated) await existingEpisode.updateOne({ documents });
          // }
          // continue;
          // ---------------------------------

          const {
            crdate, // -> createdAt
            hidden, // -> enabled
            deleted, // -> deleted
            number, // -> number
            title, // -> title
            short_description, // -> abstract
            long_description, // -> body
            image, // -> image
            documents, // -> documents
            downloadable, // -> downloadable
            downloads, // -> downloads
            plays, // -> plays
            avg_rating, // -> rating
            fk_season, // -> season
            fk_language, // -> language
            fk_media_links, // -> mediaLinks
            fk_hosts, // -> hosts
            fk_guests, // -> guests
            fk_tags, // -> tags
            fk_categories, // -> categories
            fk_links, // -> links
            fk_import_ids, // -> importIDs
            fk_subtitles, // -> subtitles
            // fk_videos, // -> videos
            videos_title, // -> videosTitle
            tx_amsbroadcast_first_air_date, // -> firstAirDate
            tx_amsbroadcast_vod_start_time, // -> videoOnDemandStartsAt
            tx_amsbroadcast_vod_end_time, // -> videoOnDemandEndsAt
            tx_amsbroadcast_vod_hidden, // -> videoOnDemand
          } = episode;

          const baseEpisode = {
            show,
            channel,
            enabled: !hidden,
            deleted,
            number,
            title,
            abstract: short_description,
            body: {
              type: 'doc',
              content: [],
            },
            downloadable,
            downloads,
            plays,
            rating: avg_rating,
            mediaLinks: {
              'youtube': null,
              'jetstream': null,
              'vimeo': null,
              'mp4-hd': null,
              'mp4-sd': null,
              'mp3': null,
              'hls': null,
              'bif': null,
            },
            language: fk_language.ietfoftitle
              ? fk_language.ietfoftitle.split('-')[0]
              : show.language,
            image: {},
            documents: [],
            hosts: [],
            guests: [],
            tags: _.uniq(fk_tags.map((tag) => tag.title)),
            categories: [],
            links: fk_links
              .filter((l) => l.link_type === 0 && !_.isEmpty(l.link))
              .map((l) => ({
                enabled: !l.hidden && !l.deleted,
                url: l.link,
                title: l.title || l.link,
              })),
            duration: 0,
            subtitles: fk_subtitles.map((s) => ({
              language: s.language_code,
              label: s.title || s.label || s.language_code,
              url: s.url,
            })),
            firstAirDate: tx_amsbroadcast_first_air_date,
            videoOnDemandStartsAt: tx_amsbroadcast_vod_start_time,
            videoOnDemandEndsAt: tx_amsbroadcast_vod_end_time,
            videoOnDemand: !tx_amsbroadcast_vod_hidden,
            videos: [],
            videosTitle: videos_title,
            scheduleIDs: [
              ...fk_import_ids
                .filter((imp) => imp.fk_import_source.title === 'XML')
                .map((imp) => ({
                  enabled: !imp.hidden && !imp.deleted,
                  scheduleID: imp.import_id,
                })),
            ],
            importIDs: [
              {
                type: 'typo3',
                recordID: `${episode.uid}`,
              },
            ],
          };

          // Deserialize the html
          const body = htmlToTiptap(long_description);

          if (body) {
            // If there are assets embedded, add them to storage and save their info
            await addAssetInfo(entity, body.content);
            baseEpisode.body = body;
          }

          // Image
          if (image && image.publicUrl !== '') {
            const imageFile = await importImage(image.publicUrl, entity);

            if (imageFile) {
              // Set the new image data
              baseEpisode.image = imageFile;
            }
          }

          // Documents
          for (const doc of documents) {
            if (doc.publicUrl) {
              const docFile = await importFile(doc.publicUrl, entity);

              if (docFile) {
                const { file, reference } = doc;

                baseEpisode.documents.push({
                  file: docFile,
                  title: !_.isEmpty(reference.title)
                    ? reference.title
                    : !_.isEmpty(file.title)
                      ? file.title
                      : composeCaption(docFile.originalFilename),
                  description: file.description,
                });
              }
            }
          }

          // Hosts
          if (
            !_.isEmpty(fk_hosts) &&
            !_.isEmpty(fk_hosts.tx_amsmedialibrary_persons)
          ) {
            for (const host of fk_hosts.tx_amsmedialibrary_persons) {
              const existingPerson = await Person.findOne({
                importIDs: {
                  $elemMatch: {
                    type: `typo3-channel-${channel.id}`,
                    recordID: `${host.uid}`,
                  },
                },
              });

              if (existingPerson) {
                // Update existing person's roles
                await existingPerson.updateOne({
                  roles: _.uniq([...existingPerson.roles, 'host']),
                });

                Logger.success(
                  `Successfully updated person. ID: ${existingPerson.id}`
                );

                baseEpisode.hosts = _.uniqBy(
                  [...baseEpisode.hosts, existingPerson.id],
                  (id) => id.toString()
                );
              } else {
                const basePerson = {
                  enabled: !host.hidden,
                  deleted: host.deleted,
                  gender: host.gender === 2 ? 'female' : 'male',
                  prefix: host.prefix,
                  firstName: host.first_name,
                  middleName: host.middle_name,
                  lastName: host.last_name,
                  suffix: host.suffix,
                  avatar: null,
                  image: null,
                  body: {
                    type: 'doc',
                    content: [],
                  },
                  roles: ['host'],
                  email: host.email,
                  channel,
                  importIDs: [
                    {
                      type: `typo3-channel-${channel.id}`,
                      recordID: `${host.uid}`,
                    },
                  ],
                };

                // Deserialize the html
                const personBody = htmlToTiptap(host.description);

                if (personBody) {
                  // If there are assets embedded, add them to storage and save their info
                  await addAssetInfo(entity, personBody.content);
                  basePerson.body = personBody;
                }

                if (host.thumbnail && host.thumbnail.publicUrl !== '') {
                  const imageFile = await importImage(
                    host.thumbnail.publicUrl,
                    entity
                  );

                  if (imageFile) {
                    // Set the new image data
                    basePerson.avatar = imageFile;
                  }
                }

                if (host.image && host.image.publicUrl !== '') {
                  const imageFile = await importImage(
                    host.image.publicUrl,
                    entity
                  );

                  if (imageFile) {
                    // Set the new image data
                    basePerson.image = imageFile;
                  }
                }
                const fullName =
                  `${basePerson.prefix} ${basePerson.firstName} ${basePerson.middleName} ${basePerson.lastName} ${basePerson.suffix}`.replace(
                    /undefined/g,
                    ''
                  );

                // Ensure person has a valid slug within its siblings (or create one from its name)
                const slug = await Person.getAvailableSlug(
                  slugify(fullName),
                  channel
                );

                // Create the new person
                const newPerson = await Person.create({
                  ...basePerson,
                  slug,
                  createdAt: host.crdate,
                  updatedAt: host.tstamp,
                });

                Logger.success(
                  `Successfully imported person. ID: ${newPerson.id}`
                );

                baseEpisode.hosts = _.uniqBy(
                  [...baseEpisode.hosts, newPerson.id],
                  (id) => id.toString()
                );
              }
            }
          }

          // Guests
          if (
            !_.isEmpty(fk_guests) &&
            !_.isEmpty(fk_guests.tx_amsmedialibrary_persons)
          ) {
            for (const guest of fk_guests.tx_amsmedialibrary_persons) {
              const existingPerson = await Person.findOne({
                importIDs: {
                  $elemMatch: {
                    type: `typo3-channel-${channel.id}`,
                    recordID: `${guest.uid}`,
                  },
                },
              });

              if (existingPerson) {
                // Update existing person's roles
                await existingPerson.updateOne({
                  roles: _.uniq([...existingPerson.roles, 'guest']),
                });

                Logger.success(
                  `Successfully updated person. ID: ${existingPerson.id}`
                );

                baseEpisode.guests = _.uniqBy(
                  [...baseEpisode.guests, existingPerson.id],
                  (id) => id.toString()
                );
              } else {
                const basePerson = {
                  enabled: !guest.hidden,
                  deleted: guest.deleted,
                  gender: guest.gender === 2 ? 'female' : 'male',
                  prefix: guest.prefix,
                  firstName: guest.first_name,
                  middleName: guest.middle_name,
                  lastName: guest.last_name,
                  suffix: guest.suffix,
                  avatar: null,
                  image: null,
                  body: {
                    type: 'doc',
                    content: [],
                  },
                  roles: ['guest'],
                  email: guest.email,
                  channel,
                  importIDs: [
                    {
                      type: `typo3-channel-${channel.id}`,
                      recordID: `${guest.uid}`,
                    },
                  ],
                };

                // Deserialize the html
                const guestBody = htmlToTiptap(guest.description);

                if (guestBody) {
                  // If there are assets embedded, add them to storage and save their info
                  await addAssetInfo(entity, guestBody.content);
                  basePerson.body = guestBody;
                }

                if (guest.thumbnail && guest.thumbnail.publicUrl !== '') {
                  const imageFile = await importImage(
                    guest.thumbnail.publicUrl,
                    entity
                  );

                  if (imageFile) {
                    // Set the new image data
                    basePerson.avatar = imageFile;
                  }
                }

                if (guest.image && guest.image.publicUrl !== '') {
                  const imageFile = await importImage(
                    guest.image.publicUrl,
                    entity
                  );

                  if (imageFile) {
                    // Set the new image data
                    basePerson.image = imageFile;
                  }
                }
                const fullName =
                  `${basePerson.prefix} ${basePerson.firstName} ${basePerson.middleName} ${basePerson.lastName} ${basePerson.suffix}`.replace(
                    /undefined/g,
                    ''
                  );

                // Ensure person has a valid slug within its siblings (or create one from its name)
                const slug = await Person.getAvailableSlug(
                  slugify(fullName),
                  channel
                );

                // Create the new person
                const newPerson = await Person.create({
                  ...basePerson,
                  slug,
                  createdAt: guest.crdate,
                  updatedAt: guest.tstamp,
                });

                Logger.success(
                  `Successfully imported person. ID: ${newPerson.id}`
                );

                baseEpisode.guests = _.uniqBy(
                  [...baseEpisode.guests, newPerson.id],
                  (id) => id.toString()
                );
              }
            }
          }

          // Add the categories
          for (const category of fk_categories) {
            // First try to find it for the specific entity
            let existingCategory = await Category.findOne({
              importIDs: `${category.uid}`,
              entity,
            });

            // Then, in the globals
            if (!existingCategory) {
              existingCategory = await Category.findOne({
                importIDs: `${category.uid}`,
                entity: null,
              });
            }

            // If the category exists, add it
            if (existingCategory) {
              baseEpisode.categories.push(existingCategory.id);
            }
          }

          // Media Links + duration
          for (const mediaLink of fk_media_links) {
            if (!_.isEmpty(mediaLink.link)) {
              switch (mediaLink.fk_media_format.uid) {
                // MP3
                case 3:
                  baseEpisode.mediaLinks.mp3 = {
                    link: mediaLink.link,
                    fileSize: mediaLink.file_size,
                    duration:
                      Math.floor(+new Date(mediaLink.duration) / 1000) || 0,
                  };

                  // Add duration
                  if (!baseEpisode.duration) {
                    baseEpisode.duration = baseEpisode.mediaLinks.mp3.duration;
                  }
                  break;

                // MP4 HD
                case 5:
                  baseEpisode.mediaLinks['mp4-hd'] = {
                    link: mediaLink.link,
                    aspectRatio: mediaLink.aspect_ratio,
                    fileSize: mediaLink.file_size,
                    duration:
                      Math.floor(+new Date(mediaLink.duration) / 1000) || 0,
                  };

                  // Add duration
                  if (!baseEpisode.duration) {
                    baseEpisode.duration =
                      baseEpisode.mediaLinks['mp4-hd'].duration;
                  }
                  break;

                // MP4
                case 2:
                  // Only if there is no MP4 HD
                  if (_.isEmpty(baseEpisode.mediaLinks['mp4-hd'])) {
                    baseEpisode.mediaLinks['mp4-hd'] = {
                      link: mediaLink.link,
                      aspectRatio: mediaLink.aspect_ratio,
                      fileSize: mediaLink.file_size,
                      duration:
                        Math.floor(+new Date(mediaLink.duration) / 1000) || 0,
                    };

                    // Add duration
                    if (!baseEpisode.duration) {
                      baseEpisode.duration =
                        baseEpisode.mediaLinks['mp4-hd'].duration;
                    }
                  }
                  break;

                // MP4 Mobile
                case 6:
                  baseEpisode.mediaLinks['mp4-sd'] = {
                    link: mediaLink.link,
                    aspectRatio: mediaLink.aspect_ratio,
                    fileSize: mediaLink.file_size,
                    duration:
                      Math.floor(+new Date(mediaLink.duration) / 1000) || 0,
                  };

                  // Add duration
                  if (!baseEpisode.duration) {
                    baseEpisode.duration =
                      baseEpisode.mediaLinks['mp4-sd'].duration;
                  }
                  break;

                // m3u8
                case 8:
                  baseEpisode.mediaLinks.hls = {
                    link: mediaLink.link,
                    aspectRatio: mediaLink.aspect_ratio,
                    fileSize: mediaLink.file_size,
                    duration:
                      Math.floor(+new Date(mediaLink.duration) / 1000) || 0,
                  };

                  // Add duration
                  if (!baseEpisode.duration) {
                    baseEpisode.duration = baseEpisode.mediaLinks.hls.duration;
                  }
                  break;

                // BIF
                case 14:
                  baseEpisode.mediaLinks.bif = {
                    link: mediaLink.link,
                    aspectRatio: mediaLink.aspect_ratio,
                    fileSize: mediaLink.file_size,
                    duration:
                      Math.floor(+new Date(mediaLink.duration) / 1000) || 0,
                  };

                  // Add duration
                  if (!baseEpisode.duration) {
                    baseEpisode.duration = baseEpisode.mediaLinks.bif.duration;
                  }
                  break;

                // Jetstream
                case 13: {
                  const jetstreamVideo = await jetstream.getVideo(
                    mediaLink.link
                  );

                  if (!jetstreamVideo) break;

                  const jetstreamMediaLink =
                    jetstream.getMediaLink(jetstreamVideo);
                  baseEpisode.mediaLinks.jetstream = jetstreamMediaLink;

                  // Set image
                  if (
                    _.isEmpty(baseEpisode.image) &&
                    jetstreamVideo.primaryThumbnail?.largeUrl
                  ) {
                    const imageFile = await importImage(
                      jetstreamVideo.primaryThumbnail.largeUrl,
                      entity
                    );

                    if (imageFile) {
                      // Set the new image data
                      baseEpisode.image = imageFile;
                    }
                  }

                  // Add duration
                  if (!baseEpisode.duration) {
                    baseEpisode.duration = jetstreamMediaLink.duration;
                  }

                  break;
                }

                // YouTube
                case 7: {
                  const youTubeMediaLink = await youtube.getMediaLink(
                    mediaLink.link
                  );

                  if (!youTubeMediaLink) break;

                  baseEpisode.mediaLinks.youtube = youTubeMediaLink;

                  // Set image
                  if (_.isEmpty(baseEpisode.image)) {
                    const imageUrl = youTubeMediaLink.thumbnails?.maxres?.url;
                    if (imageUrl) {
                      const imageFile = await importImage(imageUrl, entity);

                      if (imageFile) {
                        // Set the new image data
                        baseEpisode.image = imageFile;
                      }
                    }
                  }

                  // Add duration
                  if (!baseEpisode.duration) {
                    baseEpisode.duration = youTubeMediaLink.duration;
                  }
                  break;
                }

                default:
                  break;
              }
            }
          }

          // Season
          if (fk_season && fk_season.uid) {
            const season = await Season.findOne({
              show,
              importID: `${fk_season.uid}`,
            });

            if (season) {
              baseEpisode.season = season._id;
            }
          }

          if (existingEpisode) {
            // Update the existing episode
            await existingEpisode.updateOne(baseEpisode);

            Logger.success(
              `Successfully updated episode. ID: ${existingEpisode.id}`
            );
          } else {
            // Get the episode's slug
            let slug = await getSlug({
              ky,
              resourceId: episode.uid,
              tablename: EPISODES_TABLE,
            });

            // If slug is empty empty, generate it
            if (_.isEmpty(slug)) {
              slug = slugify(episode.title);
            }

            // Ensure episode has a valid slug within its siblings
            slug = await Episode.getAvailableSlug(slug, show);

            // Create the new episode
            existingEpisode = await Episode.create({
              ...baseEpisode,
              slug,
              createdAt: crdate,
              updatedAt: episode.tstamp,
            });

            Logger.success(
              `Successfully imported episode. ID: ${existingEpisode.id}`
            );
          }

          importCount += 1;
        }

        // Increase the offset
        offset += limit;
      }
    }

    if (importCount === 0) {
      Logger.info('No episodes found to import');
    } else {
      Logger.info(`${importCount} episodes imported`);
    }
  } catch (error) {
    Logger.error('Error when trying to import episodes:', error);
  }
};

const importVideos = async function (ky, channel, entity, lastSync = 0) {
  try {
    Logger.info('Importing videos');

    const params = {
      type: 2000,
      table: EPISODES_TABLE,
      sorting: 'tstamp ASC',
      related: 1,
      filter: {
        'fk_channel': {
          operator: '=',
          value: channel.importIDs[0],
        },
        'CONDITION': 'AND',
        'tx_amsbroadcast_vod_hidden': { operator: '=', value: false },
        'CONDITION-1': 'AND',
        'tstamp': { operator: '>=', value: lastSync },
        'CONDITION-2': 'AND',
        'fk_videos': {
          operator: '<>',
          value: '[]',
        },
        // 'CONDITION-3': 'AND',
        // fk_show: {
        //   operator: '=',
        //   value: 354,
        // },
        // 'CONDITION-3': 'AND',
        // uid: {
        //   operator: '=',
        //   value: 9579,
        // },
      },
    };

    const episodeCount = await ky
      .get('', {
        searchParams: { ...params, count: 1 },
      })
      .json();

    let importCount = 0;
    const limit = 10;
    let offset = 0;

    const { numberOfRecords } = episodeCount;

    if (numberOfRecords > 0) {
      while (offset < numberOfRecords) {
        Logger.info(
          `Looping through episodes ${offset + 1} to ${Math.min(
            limit + offset,
            numberOfRecords
          )} of ${numberOfRecords}\n`
        );

        const episodes = await ky
          .get('', {
            searchParams: { ...params, limit: `${offset},${limit}` },
          })
          .json();

        for (const episode of episodes) {
          if (_.isEmpty(episode.title)) {
            Logger.warning(
              `Ignoring episode with UID ${episode.uid} because its title is empty!`
            );
            continue;
          }

          const show = await Show.findOne({
            channel,
            importIDs: {
              $elemMatch: {
                type: 'typo3',
                recordID: `${
                  typeof episode.fk_show === 'object'
                    ? episode.fk_show.uid
                    : episode.fk_show
                }`,
              },
            },
          });

          if (!show) {
            Logger.warning(
              `Ignoring episode with UID ${episode.uid} because it has no show`
            );
            continue;
          }

          const existingEpisode = await Episode.findOne({
            show,
            importIDs: {
              $elemMatch: { type: 'typo3', recordID: `${episode.uid}` },
            },
          });

          if (existingEpisode && episode.fk_videos.length > 0) {
            Logger.info(
              `Getting videos for episode. ID: ${existingEpisode.id}`
            );

            // Create all videos
            const episodeVideos = [];
            for (const video of episode.fk_videos) {
              const videoId = await importVideo(
                ky,
                video.uid,
                show,
                existingEpisode,
                entity
              );

              if (videoId) {
                importCount += 1;
                episodeVideos.push(videoId);
              }
            }

            // Add videos to episode
            if (episodeVideos.length > 0) {
              Logger.info(
                `Adding videos to episode. ID: ${existingEpisode.id}`
              );
              existingEpisode.videos = episodeVideos;
              await existingEpisode.save();
            }
          }
        }

        // Increase the offset
        offset += limit;
      }
    }

    if (importCount === 0) {
      Logger.info('No videos found');
    } else {
      Logger.info(`${importCount} videos imported`);
    }
  } catch (error) {
    Logger.error('Error when trying to import videos:', error);
  }
};

const BUCmappings = {
  'adventist.uk': {
    entityId: '65e8eca2d9988b1907693f6c', // British Union Conference
    siteId: '6683b5b6538f496d9278787c', // BUC News
    articlePageId: '6683b5b6538f496d9278788a',
  },
  'nec.adventist.uk': {
    entityId: '65e8eca2d9988b1907693fb3', // North England Conference
    siteId: '6690fadf2611241e03286709', // NEC Website
    articlePageId: '6690fadf2611241e03286746',
  },
  'sec.adventist.uk': {
    entityId: '65e8eca2d9988b1907693f7d', // South England Conference
    siteId: '668bd33b911cc2abb02dcfbf', // SEC Website
    articlePageId: '668bd33b911cc2abb02dcff9',
  },
  'wm.adventist.uk': {
    entityId: '65e8eca2d9988b1907693fc5', // Welsh Mission
    siteId: '6690fb542611241e03287261', // WM Website
    articlePageId: '6690fb542611241e0328729b',
  },
  'adventist.ie': {
    entityId: '65e8eca2d9988b1907693faa', // Irish Mission
    siteId: '6690fb2b2611241e03286e06', // IM Website
    articlePageId: '6690fb2b2611241e03286e40',
  },
  'adventist.scot': {
    entityId: '65e8eca2d9988b1907693fbc', // Scottish Mission
    siteId: '6690fb042611241e03286a4f',
    articlePageId: '6690fb042611241e03286a8c',
  },
};

export function uid(length = 8, except = []) {
  const id = Math.random().toString(36).slice(-length);

  // Try
  if (except.includes(id)) {
    return uid(length, except);
  }

  return id;
}

export const importArticles = async function (
  ky,
  entity,
  site,
  articlePage,
  flagMappings,
  lastSync = 0
) {
  try {
    if (!site) {
      Logger.error('No site provided!');
      return 0;
    }
    let siteId = site.id;

    Logger.info('Importing articles');

    // Creates mappings object: key=pid, value=flag
    const pidFlagMappings = Object.keys(flagMappings).reduce((acc, flag) => {
      const pid = flagMappings[flag];
      if (pid) acc[pid] = flag;
      return acc;
    }, {});

    const pids = Object.keys(pidFlagMappings);

    if (pids.length === 0) {
      Logger.warning('No flags were configured, no articles will be imported');
      return;
    }

    for (const pid of pids) {
      const params = {
        type: 2000,
        table: ARTICLES_TABLE,
        sorting: 'tstamp ASC',
        related: 1,
        filter: {
          tstamp: { operator: '>=', value: lastSync },
          CONDITION: 'AND',
          pid: { operator: '=', value: pid },
          // 'CONDITION-2': 'AND',
          // 'uid': {
          //   operator: '=',
          //   value: 2059, // ASI UK with New Leadership: A Fresh Commitment to Evangelism in the UK (adventist.uk, sec.adventist.uk)
          //   // value: 152, // Vol 122 Issue 24 (BUC article with file)
          // },
        },
      };

      const articleCount = await ky
        .get('', {
          searchParams: { ...params, count: 1 },
        })
        .json();

      const articleFlag = pidFlagMappings[pid];

      let importCount = 0;
      const limit = 10;
      let offset = 0;

      const { numberOfRecords } = articleCount;

      if (numberOfRecords > 0) {
        Logger.info(
          `Attempting to import ${numberOfRecords} articles (flag: ${articleFlag}, pid: ${pid})`
        );

        while (offset < numberOfRecords) {
          Logger.info(
            `Importing articles ${offset + 1} to ${Math.min(
              limit + offset,
              numberOfRecords
            )} of ${numberOfRecords}\n`
          );

          const articles = await ky
            .get('', {
              searchParams: { ...params, limit: `${offset},${limit}` },
            })
            .json();

          for (const article of articles) {
            // Get the article's slug
            const slug = await getSlug({
              ky,
              resourceId: article.uid,
              tablename: ARTICLES_TABLE,
            });

            if (_.isEmpty(slug)) {
              Logger.warning(
                `Not importing article with external UID ${article.uid} because it doesn't have a slug`
              );
              continue;
            }

            const {
              tstamp,
              crdate,
              hidden,
              deleted,
              title,
              subtitle,
              abstract,
              location,
              author,
              description,
              image,
              fk_categories,
              fk_tags,
              publication_date,
              files,
              // articletype,
            } = article;

            const baseArticle = {
              enabled: !hidden,
              status: 'approved',
              slug,
              deleted,
              title: title,
              abstract: abstract,
              subtitle: subtitle,
              author: author,
              // image: {},
              body: {
                type: 'doc',
                content: [],
              },
              categories: [],
              tags: _.uniq(fk_tags.map((tag) => tag.title)),
              language: entity.language,
              entity,
              // sites: new Map(), // NOTE: deprecated
              canonicalSitePage: articlePage,
              importIDs: [`${article.uid}`],
              importPid: pid,
            };

            // Set location
            if (location) {
              baseArticle.location = {
                placeName: `${location}`.trim(),
              };
            }

            // NOTE: deprecated
            // Publish the article on the site
            // baseArticle.sites.set(site.id, {
            //   enabled: true,
            //   flags: { [articleFlag]: true },
            //   startsAt: _.isEmpty(publication_date)
            //     ? new Date()
            //     : new Date(publication_date),
            // });

            // Deserialize the html
            const body = htmlToTiptap(description);

            if (body) {
              // If there are assets embedded, add them to storage and save their info
              await addAssetInfo(entity, body.content);
              baseArticle.body = body;
            }

            const isBUCimport =
              entity.name === 'British Union Conference' ||
              entity.id === '65e8eca2d9988b1907693f6c';

            // Special case for articles import for British Union Conference
            // The articles are distributed to different entities based on the categories
            if (isBUCimport) {
              const hasBUCcategory = fk_categories.find(
                (category) => category.title === 'adventist.uk'
              );

              // Set organizations
              baseArticle.organizations = fk_categories.reduce(
                (acc, category) =>
                  BUCmappings[category.title]
                    ? acc.concat(BUCmappings[category.title].entityId)
                    : acc,
                []
              );

              // Get primary category (adventist.uk, sec.adventist.uk, etc.)
              // a) One category: use it
              // b) Two categories (including adventist.uk): use the non-adventist.uk category
              // c) Two or more categories: use adventist.uk
              const primaryCategory =
                fk_categories.length === 1
                  ? fk_categories[0]
                  : fk_categories.length === 2 && hasBUCcategory
                    ? fk_categories.find((c) => c.title !== 'adventist.uk')
                    : fk_categories.length >= 2
                      ? fk_categories.find((c) => c.title === 'adventist.uk')
                      : null;

              if (primaryCategory && BUCmappings[primaryCategory.title]) {
                // Set entity
                baseArticle.entity =
                  BUCmappings[primaryCategory.title].entityId;

                // Set syndication (all entities except the current one)
                baseArticle.syndicatedIn = baseArticle.organizations.filter(
                  (entityId) => entityId !== baseArticle.entity
                );

                // Set site
                // eslint-disable-next-line prefer-destructuring
                siteId = BUCmappings[primaryCategory.title].siteId;

                // Set canonical URL
                baseArticle.canonicalSitePage =
                  BUCmappings[primaryCategory.title].articlePageId;
              }

              // Remove categories
              baseArticle.categories = [];
            }

            let existingArticle = await Article.findOne({
              $or: [{ entity }, { entity: baseArticle.entity }],
              importIDs: `${article.uid}`,
            });

            if (existingArticle) {
              Logger.info(
                `Updating article with external UID ${article.uid} and local ID ${existingArticle.id}`
              );
            } else {
              Logger.info(`Importing article with UID ${article.uid}`);
            }

            // If the article has a featured image
            if (image && image.publicUrl !== '') {
              // Only import images that are not already imported or have an error
              if (
                !existingArticle ||
                !existingArticle.image ||
                existingArticle.image?.caption === 'Import error'
              ) {
                const { publicUrl, file, reference } = image;
                const imageFile = await importImage(publicUrl, entity);

                if (imageFile) {
                  // Set the new image data
                  baseArticle.image = {
                    file: imageFile,
                    caption: !_.isEmpty(reference.title)
                      ? reference.title
                      : composeCaption(imageFile.originalFilename),
                    description: reference.description || '',
                    author: file.creator,
                    takenAt: file.content_creation_date,
                    location: composeLocation(file),
                    source: file.source,
                  };
                } else {
                  // Add this so we know there was a problem
                  baseArticle.image = {
                    file: {},
                    caption: 'Import error',
                    alt: publicUrl,
                  };
                }
              }
            }

            // Add files to article
            if (
              !existingArticle ||
              files.length !== existingArticle.files?.length
            ) {
              baseArticle.files = [];
              for (const file of files) {
                if (file.publicUrl) {
                  const articleFile = await importFile(
                    file.publicUrl,
                    typeof baseArticle.entity === 'string' // can happen for BUC import
                      ? { id: baseArticle.entity }
                      : baseArticle.entity
                  );
                  if (articleFile) {
                    baseArticle.files.push({
                      id: uid(
                        6,
                        baseArticle.files.map((f) => f.id)
                      ),
                      name: file.file?.title || '',
                      file: articleFile,
                    });
                  }
                }
              }
            }

            // Add the categories (except for BUC import)
            if (!isBUCimport) {
              for (const category of fk_categories) {
                // First try to find it for the specific entity
                let existingCategory = await Category.findOne({
                  importIDs: `${category.uid}`,
                  entity,
                });

                // Then, in the globals
                if (!existingCategory) {
                  existingCategory = await Category.findOne({
                    importIDs: `${category.uid}`,
                    entity: null,
                  });
                }

                // If the category exists, add it
                if (existingCategory) {
                  baseArticle.categories.push(existingCategory.id);
                }
              }
            }

            if (existingArticle) {
              // Update the existing article
              await existingArticle.updateOne(baseArticle);

              Logger.success(
                `Successfully updated article. ID: ${existingArticle.id}`
              );
            } else {
              // Create the new article
              existingArticle = await Article.create({
                ...baseArticle,
                createdAt: crdate,
                updatedAt: tstamp,
              });

              Logger.success(
                `Successfully imported article. ID: ${existingArticle.id}`
              );
            }

            // Create the article site (replacing article.sites)
            const baseArticleSite = {
              enabled: true,
              startsAt: _.isEmpty(publication_date)
                ? new Date()
                : new Date(publication_date),
              flags: { [articleFlag]: true },
            };

            // BUC import: add article site for each article category (BUC, North England, South England, etc.)
            if (isBUCimport) {
              for (const category of fk_categories) {
                const _siteId = BUCmappings[category.title]?.siteId;
                if (_siteId) {
                  const existingArticleSite = await ArticleSite.findOne({
                    article: existingArticle,
                    site: _siteId,
                  });

                  if (existingArticleSite) {
                    // Update the existing article site
                    await existingArticle.updateOne(baseArticleSite);
                  } else {
                    // Create the new article site
                    await ArticleSite.create({
                      ...baseArticleSite,
                      article: existingArticle,
                      site: _siteId,
                    });
                  }
                }
              }
            }

            // Add article site
            else {
              const existingArticleSite = await ArticleSite.findOne({
                article: existingArticle,
                site: siteId,
              });

              if (existingArticleSite) {
                // Update the existing article site
                await existingArticleSite.updateOne(baseArticleSite);
              } else {
                // Create the new article site
                await ArticleSite.create({
                  ...baseArticleSite,
                  article: existingArticle,
                  site: siteId,
                });
              }
            }

            importCount += 1;
          }

          // Increase the offset
          offset += limit;
        }
      }

      if (importCount === 0) {
        Logger.info('No articles found to import');
      } else {
        Logger.info(`${importCount} articles imported`);
      }
    }
  } catch (err) {
    Logger.error('Error when trying to import articles:', err);
  }
};

export const importMediaLibrary = async function (ky, entity, lastSync = 0) {
  try {
    const channels = await Channel.find({
      entity,
      'importIDs.0': { $exists: true },
    });

    for (const channel of channels) {
      Logger.info(
        `Starting media library import for channel: ${channel.title}`
      );

      await importShows(ky, channel, entity, lastSync);

      await importEpisodes(ky, channel, entity, lastSync);

      await importVideos(ky, channel, entity, lastSync);
    }
  } catch (err) {
    Logger.error('Error when trying to import media library:', err);
  }
};
