import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';
import Logger from '#utils/logger.js';
import { notifyAdmins } from '#utils/notifier.js';
import { getBaseURL, getHeaders } from './utils.js';
import { addDomainToDNS } from './addDomainDNS.js';

const { REGISTER_DOMAINS, DNS_REGISTER, DNS_PROVIDER_TOKEN } = process.env;

// addDomain function shared by vercel and scalingo
export const addDomain = async ({
  domain = '',
  design = '',
  region = '',
  addRedirect = false,
  zoneIdentifier = '',
  cnameContent = '',
  provider = {},
}) => {
  if (REGISTER_DOMAINS && domain && provider) {
    const { params, supportsRedirects, defaultRegion } = provider;
    region = region || defaultRegion;

    if (domain.startsWith('copy-of')) {
      Logger.info('Not adding domain because it starts with "copy-of"');
      return false;
    }

    if (domain.includes('localhost')) {
      Logger.info('Not adding domain because is a local dev domain');
      return false;
    }

    // cancel adding a "www." redirect domain if is a staging / migration domain.
    if (domain.endsWith('.hopeplatform.org')) {
      addRedirect = false;
    }

    const baseUrl = getBaseURL(provider.baseUrl, {
      design,
      region,
    });

    // Adding subdomain to DNS provider
    const subdomain = domain.split('.')[0];

    // Only register in DNS provider if zoneIdentifier is provided and DNS_REGISTER is true
    if (
      `${DNS_REGISTER}`.toLowerCase() === 'true' &&
      DNS_PROVIDER_TOKEN &&
      region &&
      zoneIdentifier &&
      subdomain &&
      cnameContent
    ) {
      await addDomainToDNS({
        domain,
        region,
        zoneIdentifier,
        subdomain,
        cnameContent,
      });
    }

    // Adding domain to hosting provider
    const headers = await getHeaders(provider);

    Logger.info('Attempting to add domain:', domain, 'to region:', region);

    // TODO: Before this check in sites and entity(automated-sites) if the domain is already added

    try {
      // Create a client for the hosting provider API
      const providerClient = createExternalApiClient({
        baseUrl,
        timeout: 30000,
      });

      const data = await providerClient
        .get(`${params || ''}`, {
          headers,
          searchParams: { limit: 100, redirects: false },
        })
        .json();

      // Attempt to add domain only if it's not already added
      if (!data.domains.find((d) => d.name === domain)) {
        await providerClient.post(`${params || ''}`, {
          json: { name: domain },
          headers,
        });

        const shouldAddRedirects = addRedirect && supportsRedirects;

        if (shouldAddRedirects) {
          await providerClient.post(`${params || ''}`, {
            json: {
              name: `www.${domain}`,
              redirect: domain,
              redirectStatusCode: 308,
            },
            headers,
          });
        }

        notifyAdmins({
          subject: 'Successfully added domain',
          templateName: 'domainRegistration',
          templateValues: {
            domain,
            region,
            redirects: shouldAddRedirects ? [`www.${domain}`] : null,
            isError: false,
          },
        });

        Logger.success('Domain added:', domain, 'to region:', region);

        return true;
      }

      Logger.info('Domain already added:', domain, 'to region:', region);

      return false;
    } catch (error) {
      // Use standardized error handling
      const normalizedError = await handleKyError(
        error,
        `adding domain ${domain} to ${provider.name}`
      );

      notifyAdmins({
        subject: 'Error adding domain',
        templateName: 'domainRegistration',
        templateValues: {
          domain,
          region,
          isError: true,
          error: JSON.stringify(normalizedError),
        },
      });

      Logger.error(
        `Couldn't add domain ${domain} to region ${region} in ${provider.name}:`,
        normalizedError
      );

      return false;
    }
  }

  Logger.warning(
    "Domain registration not possible. Make sure it's enabled in env vars and the provided domain is not empty:",
    { REGISTER_DOMAINS, domain, region }
  );

  return false;
};
