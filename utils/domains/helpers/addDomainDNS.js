import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';
import Logger from '#utils/logger.js';
import { notifyAdmins } from '#utils/notifier.js';

const { DNS_PROVIDER, DNS_PROVIDER_TOKEN } = process.env;

export async function addDomainToDNS({
  domain,
  region,
  zoneIdentifier,
  subdomain,
  cnameContent,
}) {
  // Create a client for Cloudflare DNS API
  const dnsClient = createExternalApiClient({
    baseUrl: `https://api.cloudflare.com/client/v4/zones/${zoneIdentifier}`,
    headers: {
      Authorization: `Bearer ${DNS_PROVIDER_TOKEN}`,
    },
    timeout: 20000,
  });

  try {
    const data = await dnsClient.get('dns_records').json();

    // Attempt to add domain only if it's not already added
    if (!data.result.find((r) => r.name.split('.')[0] === subdomain)) {
      Logger.info('Attempting to add domain to DNS provider:', domain);
      const response = await dnsClient.post('dns_records', {
        json: {
          content: cnameContent,
          name: subdomain,
          type: 'CNAME',
          ttl: 1, // 1 means auto
          tags: ['added_by_zafir'], // This tag is used to later identify and remove the record should the need arise.
          comment:
            'Added by Hope Media Europe e.V. automatically through the API',
        },
      });

      if (response.status === 200) {
        Logger.success(
          'Domain added to DNS provider:',
          domain,
          'to CNAME:',
          cnameContent,
          'in zone:',
          zoneIdentifier
        );
      } else {
        const errorData = await response.json();
        Logger.error(errorData);
      }
    } else {
      Logger.info('Domain already added to DNS provider:', domain);
    }
  } catch (error) {
    const normalizedError = await handleKyError(
      error,
      `adding DNS record for ${domain}`
    );

    notifyAdmins({
      subject: 'Error adding domain to DNS provider',
      templateName: 'domainRegistration',
      templateValues: {
        domain,
        region,
        isError: true,
        error: JSON.stringify(normalizedError),
      },
    });
    Logger.error(
      `Couldn't add DNS record for ${domain} in ${DNS_PROVIDER}:`,
      normalizedError
    );
  }
}
