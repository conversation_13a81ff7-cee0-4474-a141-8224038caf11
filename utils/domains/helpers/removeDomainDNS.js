import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';
import Logger from '#utils/logger.js';
import { notifyAdmins } from '#utils/notifier.js';

const { DNS_PROVIDER, DNS_PROVIDER_TOKEN } = process.env;

export async function removeDomainFromDNS({
  domain,
  region,
  zoneIdentifier,
  subdomain,
}) {
  // Create a client for Cloudflare DNS API
  const dnsClient = createExternalApiClient({
    baseUrl: `https://api.cloudflare.com/client/v4/zones/${zoneIdentifier}`,
    headers: {
      Authorization: `Bearer ${DNS_PROVIDER_TOKEN}`,
    },
    timeout: 20000,
  });

  try {
    const data = await dnsClient
      .get('dns_records', {
        searchParams: { tag: 'added_by_zafir' }, // This tag is used to identify and remove the record should the need arise. // TODO: Rename this to AWE
      })
      .json();

    // Attempt to remove domain only if it exists
    const matchingSubdomain = data.result.find(
      (r) => r.name.split('.')[0] === subdomain
    );
    if (matchingSubdomain) {
      Logger.info('Attempting to remove domain from DNS provider:', domain);
      const response = await dnsClient.delete(
        `dns_records/${matchingSubdomain.id}`
      );

      if (response.status === 200) {
        Logger.success('Domain removed from DNS provider:', domain);
      }
    } else {
      Logger.info('Domain already removed from DNS provider:', domain);
    }
  } catch (error) {
    const normalizedError = await handleKyError(
      error,
      `removing DNS record for ${domain}`
    );

    notifyAdmins({
      subject: 'Error removing domain from DNS provider',
      templateName: 'domainRegistration',
      templateValues: {
        domain,
        region,
        isError: true,
        error: JSON.stringify(normalizedError),
      },
    });
    Logger.error(
      `Couldn't remove DNS record for ${domain} in ${DNS_PROVIDER}:`,
      normalizedError
    );
  }
}
