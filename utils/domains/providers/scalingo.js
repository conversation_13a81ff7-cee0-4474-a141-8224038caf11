import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';
import { addDomain } from '../helpers/addDomain.js';
import { removeDomain } from '../helpers/removeDomain.js';

const { SCALINGO_API_TOKEN } = process.env;

export const scalingo = {
  name: 'Scalingo',
  baseUrl:
    'https://api.{{region}}.scalingo.com/v1/apps/{{design}}-osc-fr1/domains',
  getDeletePath: (domain, data) => {
    const { id: domainId } = data.domains.find((d) => d.name === domain);
    return `${domainId}`;
  },
  defaultRegion: 'osc-fr1', // Note: This is the default region for Scalingo, and the only one, it is equivalent to eu-central.
  getBearerToken: async () => {
    const credentials = Buffer.from(`:${SCALINGO_API_TOKEN}`).toString(
      'base64'
    );

    // Create a client for Scalingo auth API
    const authClient = createExternalApiClient({
      baseUrl: 'https://auth.scalingo.com/v1',
      timeout: 15000,
    });

    try {
      const data = await authClient
        .post('tokens/exchange', {
          body: '',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': `Basic ${credentials}`,
          },
        })
        .json();
      const { token } = data;

      return token;
    } catch (error) {
      const normalizedError = await handleKyError(
        error,
        'getting Scalingo bearer token'
      );
      throw normalizedError;
    }
  },
  addDomain: async (config) => await addDomain(config),
  removeDomain: async (config) => await removeDomain(config),
};
