import ky from 'ky';
import { logDebug } from '#utils/logger.js';

/**
 * Default retry configuration for external APIs
 * Provides comprehensive retry behavior with exponential backoff
 */
const DEFAULT_EXTERNAL_RETRY = {
  limit: 3,
  methods: ['get', 'post', 'put', 'patch', 'delete'],
  statusCodes: [408, 413, 429, 500, 502, 503, 504],
  backoffLimit: 30000, // 30 seconds maximum delay
};

/**
 * Creates a ky instance optimized for external API calls
 * @param {Object} options - Configuration options
 * @param {string} [options.baseUrl] - Base URL for the API (can also use prefixUrl)
 * @param {string} [options.prefixUrl] - Prefix URL for the API (alternative to baseUrl)
 * @param {Object} [options.headers] - Default headers
 * @param {number} [options.timeout=30000] - Request timeout in milliseconds
 * @param {Object} [options.retry] - Retry configuration
 * @param {boolean} [options.enableLogging=false] - Enable request/retry logging
 * @param {Object} [options.hooks] - Additional ky hooks
 * @returns {import('ky').KyInstance} Configured ky instance
 */
export const createExternalApiClient = ({
  baseUrl,
  prefixUrl,
  headers = {},
  timeout = 30000, // 30 seconds for external APIs
  retry = DEFAULT_EXTERNAL_RETRY,
  enableLogging = false,
  hooks = {},
  ...otherOptions
} = {}) => {
  const config = {
    prefixUrl: prefixUrl || baseUrl,
    timeout,
    retry,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    ...otherOptions,
  };

  // Add logging hooks if enabled
  if (enableLogging) {
    config.hooks = {
      beforeRequest: [
        (request) => {
          // Log outgoing requests for debugging
          logDebug(`Making request to: ${request.url}`);
        },
        ...(hooks.beforeRequest || []),
      ],
      beforeRetry: [
        async ({ request, _options, error, retryCount }) => {
          logDebug(`Retrying request (${retryCount}): ${request.url}`);
          logDebug(`Error: ${error.message}`);
        },
        ...(hooks.beforeRetry || []),
      ],
      ...hooks,
    };
  } else if (Object.keys(hooks).length > 0) {
    config.hooks = hooks;
  }

  return ky.create(config);
};
