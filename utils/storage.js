import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Upload } from '@aws-sdk/lib-storage';
import { GetObjectCommand, PutObjectCommand, S3 } from '@aws-sdk/client-s3';
import fs from 'fs';
import multer from 'multer';
import path, { extname } from 'path';
import mimeTypes from 'mime-types';
import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';

import { parseBytes } from '#utils/bytes.js';
import { errorCodes, generateError } from '#utils/appError.js';
import { randomString } from '#utils/strings.js';
import { logError, logInfo } from '#utils/logger.js';

/**
 * Get an S3 client
 * @param {Object} options Options object
 * @param {String} options.endpoint Endpoint of the S3 service
 * @param {String} options.secretAccessKey Secret access key for the S3 service
 * @param {String} options.accessKeyId Access key ID for the S3 service
 * @returns {S3} The S3 client
 */
export function getS3Client({ endpoint, secretAccessKey, accessKeyId } = {}) {
  return new S3({
    endpoint,
    region: 'us-east-1', // The region is hardcoded as AWS S3 does not support custom endpoints withouth a region
    credentials: {
      secretAccessKey,
      accessKeyId,
    },
  });
}

/**
 * Generate a random filename for a file to be uploaded (3 random characters + timestamp)
 * @returns {String} The random filename
 * @example randomFilename() // 'abc123456789'
 */
export function randomFilename() {
  return `${randomString({ length: 3 })}${+new Date()}`;
}

const defaultGetUploadOptions = {
  folder: 'images',
  maxFileSize: '10mb',
  maxFileCount: 1,
  allowedContentTypes: [],
};
/**
 * Get a multer upload middleware
 * @param {Object} options Options object
 * @param {String} options.folder Folder to upload the files to
 * @param {String} options.maxFileSize Maximum file size allowed
 * @param {Number} options.maxFileCount Maximum number of files allowed
 * @param {String[]} options.allowedContentTypes Allowed content types for the files
 * @returns {Function} The multer middleware
 */
export function getUpload(options = defaultGetUploadOptions) {
  const { folder, maxFileSize, maxFileCount, allowedContentTypes } = {
    ...defaultGetUploadOptions,
    ...options,
  };

  const multerStorage = multer.diskStorage({
    destination: `temp/${folder}`,
    filename: (req, file, cb) => {
      const ext = extname(file.originalname);
      cb(null, `${randomFilename()}${ext}`);
    },
  });

  const multerFilter = (req, file, cb) => {
    const mimeTypeByExtension = mimeTypes.lookup(extname(file.originalname));

    if (
      allowedContentTypes.length > 0 &&
      !(
        allowedContentTypes.includes(file.mimetype) ||
        allowedContentTypes.includes(mimeTypeByExtension)
      )
    ) {
      cb(
        generateError(
          `Mime type '${file.mimetype}' not allowed for '${file.fieldname}' field`,
          errorCodes.VALIDATION_ERROR,
          422,
          {
            [file.fieldname]: { type: 'MIME_NOT_ALLOWED' },
          }
        )
      );
    } else {
      cb(null, true);
    }
  };

  return multer({
    storage: multerStorage,
    fileFilter: multerFilter,
    limits: {
      fileSize: parseBytes(maxFileSize),
      files: +maxFileCount,
    },
  });
}

// Default S3 client to use for uploads
const s3 = getS3Client({
  endpoint: process.env.BUCKET_ENDPOINT,
  secretAccessKey: process.env.BUCKET_SECRET,
  accessKeyId: process.env.BUCKET_KEY_ID,
});

// Default options for the uploadFile helper
const defaultUploadFileOptions = {
  acl: 'public-read', // NOTE: Unless specified otherwise, files are uploaded as `public-read`
};

/**
 * Uploads a file to the provided bucket
 * @param {String} filePath Local path of the file to upload (i.e. '/path/to/file.jpg')
 * @param {Object} options Options object
 * @param {String} options.bucket Bucket to upload the file to (i.e. 'my-bucket')
 * @param {String} options.acl Access control list for the file (Options: 'public-read' , 'private'. Default: 'uploadFile')
 * @param {String} options.mime Mime type of the file (i.e. 'image/jpeg')
 * @param {String} options.name Name of the file to store (i.e. 'my-file')
 * @param {String} options.key Key to store the file under in the bucket (i.e. 'images')
 * @param {String} options.contentDisposition Content disposition of the file to include as metadata (i.e. 'attachment; filename="example.jpg"' to force download with a specific filename)
 * @param {Object} options.customS3Client Custom S3 client to use for the upload (i.e. new S3({ ... }))
 * @returns
 */
export function uploadFile(filePath, options = defaultUploadFileOptions) {
  const { bucket, acl, mime, name, key, contentDisposition, customS3Client } = {
    ...defaultUploadFileOptions,
    ...options,
  };

  // create the read stream for upload
  const reader = fs.createReadStream(filePath);

  // get the extension of the file
  const ext = extname(filePath);

  // generate a new random file name
  const fileName = name || randomFilename();

  // return the upload as promise
  return new Upload({
    client: customS3Client || s3,

    params: {
      Bucket: bucket,
      ACL: acl,
      Key: `${key}/${fileName}${ext}`,
      Body: reader,
      ContentType: mime,
      ContentDisposition: contentDisposition, // i.e. 'attachment; filename="example.jpg"'
    },

    partSize: 5 * 1024 * 1024,
    queueSize: 1,
  }).done();
}

/**
 * Get a list of files in a bucket
 * @param {String} bucket Bucket to get the files from
 * @param {Object} options Options object
 * @param {String} options.prefix Prefix to filter the files by
 * @returns {Promise<Object[]>} List of files in the bucket
 */
export function getFiles(bucket, options = {}) {
  return new Promise((resolve, reject) => {
    const params = {
      Bucket: bucket,
    };

    if (options.prefix) {
      params.Prefix = options.prefix;
    }

    s3.listObjects(params, (err, data) =>
      err
        ? reject(err)
        : resolve(
            data.Contents.map((f) => ({
              name: f.Key,
              size: f.Size,
            }))
          )
    );
  });
}

/**
 * Remove a file from a bucket
 * @param {String} bucket Bucket to remove the file from
 * @param {String} filename Name of the file to remove
 * @returns {Promise<boolean>} Whether the file was removed or not
 */
export function removeFile(bucket, filename) {
  return new Promise((resolve, reject) => {
    s3.deleteObject(
      {
        Bucket: bucket,
        Key: filename,
      },
      (err, data) => (err ? reject(err) : resolve(null, !!data.DeleteMarker))
    );
  });
}

/**
 * Move a file from one space to another
 * @param {Object} params
 * @param {String} params.sourceSpace Source space to move the file from
 * @param {String} params.sourceKey Source key to move the file from
 * @param {String} params.destinationSpace Destination space to move the file to
 * @param {String} params.destinationKey Destination key to move the file to
 * @param {String} params.desiredFilename Desired filename for the file
 * @param {String} params.contentType Content type of the file
 * @returns {Promise<Object>} The result of the move operation
 */
export async function moveFile({
  sourceSpace,
  sourceKey,
  destinationSpace,
  destinationKey,
  desiredFilename,
  contentType,
} = {}) {
  try {
    // Read the file from the source space

    const sourceParams = {
      Bucket: sourceSpace,
      Key: sourceKey,
    };
    s3.getObject(sourceParams, (err, data) => {
      if (err) {
        logError(
          `Error getting object ${sourceKey} from bucket ${sourceSpace}`
        );
      } else {
        const sourceStream = data.Body;

        // Upload the file to the destination space
        const params = {
          Bucket: destinationSpace,
          Key: destinationKey,
          Body: sourceStream,
          ContentType: contentType,
          ACL: 'public-read',
          ContentDisposition: `attachment; filename="${desiredFilename}"`,
        };
        s3.putObject(params, (error, putData) => {
          if (error) {
            logError(error);
          } else {
            logInfo(
              `Successfully moved object ${desiredFilename} to ${destinationKey} with ETag ${putData.ETag}`
            );
          }
        });
      }
    });

    return { success: true };
  } catch (error) {
    logError(error);
  }
}

/**
 * Get a pre-signed URL for a file in a bucket to download
 * @param {Object} params
 * @param {String} params.bucket Bucket to get the file from
 * @param {String} params.key Key of the file to get
 * @returns {String} The pre-signed URL
 */
export async function getDownloadPreSignedUrl({ bucket, key } = {}) {
  try {
    const signedUrl = await getSignedUrl(
      s3,
      new GetObjectCommand({
        Bucket: bucket,
        Key: key,
      }),
      {
        expiresIn: 60 * 60 * 24, // expires in a day
      }
    );

    return signedUrl;
  } catch (error) {
    logError(error);
  }
}

/**
 * Get a pre-signed URL for uploading a file to a bucket
 * @param {Object} params
 * @param {String} params.bucket Bucket to upload the file to
 * @param {String} params.key Key to store the file under in the bucket
 * @param {String} params.contentType Content type of the file (e.g., 'image/jpeg')
 * @param {Number} [params.expiresIn=3600] Expiration time for the URL in seconds (default: 1 hour)
 * @returns {Promise<String>} The pre-signed URL
 */
export async function getUploadPreSignedUrl({
  bucket,
  key, // Should already include the file name and extension.
  contentType,
  acl = 'public-read',
  expiresIn = 300, // 5 minutes
} = {}) {
  try {
    const signedUrl = await getSignedUrl(
      s3,
      new PutObjectCommand({
        Bucket: bucket,
        Key: key,
        ContentType: contentType,
        ACL: acl,
      }),
      { expiresIn }
    );

    return signedUrl;
  } catch (error) {
    logError(error);
    throw new Error('Failed to generate upload pre-signed URL');
  }
}

/**
 * Sanitize a string for content disposition
 * @param {String} inputString The input string to sanitize
 * @returns {String} The sanitized string
 */
export function sanitizeForContentDisposition(inputString) {
  // URL-encode the input string
  const sanitizedString = encodeURIComponent(inputString);
  return sanitizedString;
}

/**
 * Downloads any valid resource from a url to a local temp folder.
 * @param {String} url The URL of the resource to download
 * @param {String} filename The filename to save the resource as
 * @returns {Promise<string>} The path to the downloaded file
 */
export async function downloadFile(url, filename) {
  try {
    // Create a client for downloading files
    const downloadClient = createExternalApiClient({
      timeout: 60000, // Longer timeout for file downloads
    });

    const response = await downloadClient.get(url);

    // const tempFolderPath = path.join(__dirname, 'temp');
    // The tempFolderPath is in the root of the project. We don't use relative path
    const tempFolderPath = path.join('temp');
    if (!fs.existsSync(tempFolderPath)) {
      fs.mkdirSync(tempFolderPath);
    }

    const filePath = path.join(tempFolderPath, filename);
    const writer = fs.createWriteStream(filePath);

    return new Promise(async (resolve, reject) => {
      try {
        // Convert the response body to a Node.js readable stream
        const buffer = await response.arrayBuffer();
        const uint8Array = new Uint8Array(buffer);

        writer.write(uint8Array);
        writer.end();

        writer.on('finish', () => resolve(filePath));
        writer.on('error', reject);
      } catch (error) {
        reject(error);
      }
    });
  } catch (error) {
    const normalizedError = await handleKyError(
      error,
      `downloading file from ${url}`
    );
    throw normalizedError;
  }
}

export default {
  randomFilename,
  getUpload,
  uploadFile,
  getFiles,
  removeFile,
  moveFile,
  getDownloadPreSignedUrl,
  sanitizeForContentDisposition,
  downloadFile,
};
