import { describe, it, expect } from 'vitest';
import { createExternalApiClient } from '#utils/kyClient.js';

describe('HTTP Client Utilities', () => {
  describe('createExternalApiClient', () => {
    it('should create a client with default external API settings', () => {
      const client = createExternalApiClient();

      expect(client).toBeDefined();
      expect(typeof client.get).toBe('function');
      expect(typeof client.post).toBe('function');
      expect(typeof client.put).toBe('function');
      expect(typeof client.patch).toBe('function');
      expect(typeof client.delete).toBe('function');
    });

    it('should create a client with custom base URL', () => {
      const client = createExternalApiClient({
        baseUrl: 'https://api.example.com',
      });

      expect(client).toBeDefined();
    });

    it('should create a client with custom headers', () => {
      const client = createExternalApiClient({
        headers: {
          'Authorization': 'Bearer test-token',
          'X-Custom-Header': 'test-value',
        },
      });

      expect(client).toBeDefined();
    });

    it('should create a client with custom timeout', () => {
      const client = createExternalApiClient({
        timeout: 60000,
      });

      expect(client).toBeDefined();
    });

    it('should create a client with logging disabled', () => {
      const client = createExternalApiClient({
        enableLogging: false,
      });

      expect(client).toBeDefined();
    });
  });

  describe('Configuration validation', () => {
    it('should handle both baseUrl and prefixUrl parameters', () => {
      const clientWithBaseUrl = createExternalApiClient({
        baseUrl: 'https://api.example.com',
      });

      const clientWithPrefixUrl = createExternalApiClient({
        prefixUrl: 'https://api.example.com',
      });

      expect(clientWithBaseUrl).toBeDefined();
      expect(clientWithPrefixUrl).toBeDefined();
    });

    it('should prioritize prefixUrl over baseUrl when both are provided', () => {
      const client = createExternalApiClient({
        baseUrl: 'https://wrong.example.com',
        prefixUrl: 'https://correct.example.com',
      });

      expect(client).toBeDefined();
    });
  });
});
