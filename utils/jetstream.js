import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';
import fs from 'fs';

import Logger from '#utils/logger.js';
import { isArray } from '#utils/types.js';
import Episode from '#modules/media-library/models/Episode.js';
import { createOrUpdateMediaLinks } from '#modules/media-library/helpers/createOrUpdateMediaLinks.js';

const prefixes = {
  audio: 'jsa',
  video: 'jsv',
};

function getJetstreamId(id, type = 'video') {
  let jetstreamId = null;
  if (id) {
    // NOTE: Jetstream v1 used long IDs, but Jetstream v2 uses short IDs (15 symbols)
    // - Jetstream v1: jsv:r7e8dQ17fnEZvp9Q2C3N
    // - Jetstream v2: jsv:r7e8dQ17fnE
    jetstreamId = !id.startsWith(prefixes[type])
      ? `${prefixes[type]}:${id}`
      : id;

    const isStaging = isStagingId(jetstreamId);
    const maxLength = isStaging
      ? 21 // jsv:stage:12345678910
      : 15; // jsv:12345678910

    // Transform long ID to short ID
    if (jetstreamId.length > maxLength) {
      jetstreamId = jetstreamId.substring(0, maxLength);
    }
  }
  return jetstreamId;
}

function isStagingId(jetstreamId) {
  return jetstreamId.startsWith('jsv:stage:');
}

async function getVideo(id) {
  const jetstreamId = getJetstreamId(id);
  if (!jetstreamId) return null;

  try {
    const isStaging = isStagingId(jetstreamId);
    const accessToken = isStaging
      ? process.env.JETSTREAM_GRAPHQL_ACCESS_TOKEN_STAGING
      : process.env.JETSTREAM_GRAPHQL_ACCESS_TOKEN;

    const apiUrl = isStaging
      ? process.env.JETSTREAM_GRAPHQL_API_URL_STAGING
      : process.env.JETSTREAM_GRAPHQL_API_URL;

    // Create a client for Jetstream GraphQL API
    const jetstreamClient = createExternalApiClient({
      baseUrl: apiUrl,
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      timeout: 30000,
    });

    const query = {
      operationName: 'Video',
      query: `query Video($videoId: String!) {
        video(id: $videoId) {
          id
          title
          metadata {
            duration
            views
          }
          renditions {
            type
            shortUrl
            height
            filesize
            width
            status
          }
          subtitles {
            title
            languageCode
            url
          }
          primaryThumbnail {
            smallUrl
            largeUrl
          }
        }
      }`,
      variables: {
        videoId: jetstreamId,
      },
    };

    const { data, errors = [] } = await jetstreamClient
      .post('', {
        json: query,
      })
      .json();

    if (errors.length > 0) {
      // Logger.error('Error getting jetstream video', errors);
      return null;
    }

    return data?.video;
  } catch (error) {
    const normalizedError = await handleKyError(
      error,
      `getting jetstream video ${jetstreamId}`
    );
    Logger.error('Error getting jetstream video', normalizedError);
    return null;
  }
}

// It seems as if video is to be replaced with media.
async function getMedia(id, type) {
  const jetstreamId = getJetstreamId(id, type);
  if (!jetstreamId) return null;

  try {
    const isStaging = isStagingId(jetstreamId);
    const accessToken = isStaging
      ? process.env.JETSTREAM_GRAPHQL_ACCESS_TOKEN_STAGING
      : process.env.JETSTREAM_GRAPHQL_ACCESS_TOKEN;

    const apiUrl = isStaging
      ? process.env.JETSTREAM_GRAPHQL_API_URL_STAGING
      : process.env.JETSTREAM_GRAPHQL_API_URL;

    // Create a client for Jetstream GraphQL API
    const jetstreamClient = createExternalApiClient({
      baseUrl: apiUrl,
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      timeout: 30000,
    });

    const query = {
      operationName: 'Media',
      query: `query Media($mediaId: String!) {
        media(id: $mediaId) {
          id
          title
          metadata {
            duration
            views
          }
          renditions {
            type
            shortUrl
            height
            filesize
            width
            status
          }
          subtitles {
            title
            languageCode
            url
          }
          primaryThumbnail {
            smallUrl
            largeUrl
          }
        }
      }`,
      variables: {
        mediaId: jetstreamId,
      },
    };

    const response = await jetstreamClient.post('', {
      json: query,
    });

    const responseData = await response.json();
    const { data, errors = [] } = responseData;

    if (errors.length > 0) {
      // Logger.error('Error getting jetstream video', errors);
      return null;
    }

    return data?.media;
  } catch (error) {
    const normalizedError = await handleKyError(
      error,
      `getting jetstream media ${jetstreamId}`
    );
    Logger.error('Error getting jetstream media', normalizedError);
    return null;
  }
}

function getDuration(jetstreamMedia) {
  const { metadata } = jetstreamMedia;
  let duration = metadata?.duration ? Math.floor(metadata.duration / 1000) : 0;
  duration = Number.isNaN(duration) ? 0 : duration;
  return duration;
}

function getMediaLink(jetstreamMedia) {
  if (!jetstreamMedia) return null;
  const { id, renditions, subtitles, title } = jetstreamMedia;

  return {
    link: id,
    aspectRatio: '16:9',
    duration: getDuration(jetstreamMedia),
    title: title || '',
    renditions: Array.isArray(renditions)
      ? renditions.map((rendition) => ({
          type: rendition.type,
          url: rendition.shortUrl,
          width: rendition.width,
          height: rendition.height,
          filesize: rendition.filesize,
        }))
      : [],
    subtitles: Array.isArray(subtitles)
      ? subtitles.map((subtitle) => ({
          languageCode: subtitle.languageCode,
          url: subtitle.url,
        }))
      : [],
  };
}

function getRendition(jetstreamMedia, type) {
  if (!jetstreamMedia || !isArray(jetstreamMedia.renditions)) return null;
  return jetstreamMedia.renditions.find((r) => r.type === type);
}

async function getVideoEventsAggregate(organizationId) {
  if (!organizationId) {
    return null;
  }

  try {
    const accessToken = process.env.JETSTREAM_ANALYTICS_GRAPHQL_ACCESS_TOKEN;
    const apiUrl = process.env.JETSTREAM_ANALYTICS_GRAPHQL_API_URL;

    const headers = {
      'content-type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    };
    const query = {
      operationName: 'VideoMetrics',
      query: `query VideoMetrics($input: FetchVideoEventsAggregateInput!) {
        videoEventsAggregate(input: $input) {
          id
          views
        }
      }`,
      variables: {
        input: {
          filter: {
            organizationId: `hcorg:${organizationId}`,
          },
        },
      },
    };

    const response = await jetstreamClient.post(apiUrl, {
      json: query,
      headers,
    });

    const responseData = await response.json();
    const { data, errors = [] } = responseData;

    if (errors.length > 0) {
      Logger.error(
        'Error getting jetstream video events aggregate data',
        errors
      );
      return null;
    }

    return data?.videoEventsAggregate;
  } catch (error) {
    Logger.error(
      'Error getting jetstream video events aggregate data',
      error.response
    );
    return null;
  }
}

async function getVideos({
  organizationId,
  pageSize = 10,
  pageNumber = 1,
  videoIds = [],
}) {
  if (!organizationId) {
    return null;
  }

  try {
    const accessToken = process.env.JETSTREAM_GRAPHQL_ACCESS_TOKEN;
    const apiUrl = process.env.JETSTREAM_GRAPHQL_API_URL;

    const headers = {
      'content-type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    };
    const query = {
      operationName: 'Videos',
      query: `query Videos($page: Int, $limit: Int!, $filter: VideosFilter) {
        videos(page: $page, limit: $limit, filter: $filter) {
          items {
            id
            title
            metadata {
              duration
            }
            renditions {
              type
              shortUrl
              height
              filesize
              width
              status
            }
            subtitles {
              title
              languageCode
              url
            }
            primaryThumbnail {
              largeUrl
              smallUrl
            }
          }
          pageInfo {
            totalPages
            page
          }
        }
      }`,
      variables: {
        limit: pageSize,
        page: pageNumber,
        filter: {
          organizationId: `hcorg:${organizationId}`,
          ids: videoIds,
        },
      },
    };

    const response = await jetstreamClient.post(apiUrl, {
      json: query,
      headers,
    });

    const responseData = await response.json();
    const { data, errors = [] } = responseData;

    if (errors.length > 0) {
      Logger.error(
        'Error getting jetstream video events aggregate data',
        errors
      );
      return null;
    }

    return { videos: data?.videos?.items, pageInfo: data?.videos?.pageInfo };
  } catch (error) {
    // Handle ky error structure
    const errorData = error.response
      ? await error.response.json().catch(() => error.response.statusText)
      : error;
    Logger.error(
      'Error getting jetstream video events aggregate data',
      errorData
    );
    return null;
  }
}

// DOcument this
/**
 * Returns the signed upload URL for the media file. This URL is used to upload the file through HTTP PUT
 * @param {String} uploadRegion
 * @param {String} fileType
 */
export async function getMediaUploadUrl({
  uploadRegion = 'EU',
  fileType = 'AUDIO_MP3',
  origin,
} = {}) {
  try {
    const accessToken = process.env.JETSTREAM_GRAPHQL_ACCESS_TOKEN;
    const apiUrl = process.env.JETSTREAM_GRAPHQL_API_URL;

    const headers = {
      'content-type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    };

    const input = {
      uploadRegion,
      fileType,
    };

    // If origin is provided, we add it to the input
    if (origin) {
      // if origin starts with localhost add http:// at the beginning, else add https://
      input.origin = origin.startsWith('localhost')
        ? `http://${origin}`
        : `https://${origin}`;
    }

    const query = {
      operationName: 'CreateSignedMediaUploadUrl',
      query: `mutation CreateSignedMediaUploadUrl($input: CreateSignedMediaUploadUrlInput!) {
        createSignedMediaUploadUrl(input: $input) {
          url
        }
      }`,
      variables: {
        input,
      },
    };

    const response = await jetstreamClient.post(apiUrl, {
      json: query,
      headers,
    });

    const responseData = await response.json();
    const { data, errors = [] } = responseData;

    if (errors.length > 0) {
      Logger.error('Error getting jetstream signed url to upload', errors);
      return null;
    }

    Logger.info('Signed URL', data);
    return data?.createSignedMediaUploadUrl?.url;
    // return { videos: data?.videos?.items, pageInfo: data?.videos?.pageInfo };
  } catch (error) {
    Logger.error(
      'Error getting jetstream signed url to upload',
      error.response
    );

    return null;
  }
}

export async function createJetstreamMedia({
  contentType = 'AUDIO',
  title = 'Untitled',
  filesize = 0,
  mediaUrl,
  organizationId,
  originalFileName,
  code,
  attachToEpisodeId,
} = {}) {
  try {
    if (!mediaUrl) {
      throw new Error('No mediaUrl provided');
    }
    const accessToken = process.env.JETSTREAM_GRAPHQL_ACCESS_TOKEN;
    const apiUrl = process.env.JETSTREAM_GRAPHQL_API_URL;

    const headers = {
      'content-type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    };

    const query = {
      operationName: 'CreateMedia',
      query: `mutation CreateMedia($input: CreateMediaInput!) {
        createMedia(input: $input) {
          id
          title
          mediaUrl
        }
      }`,
      variables: {
        input: {
          code,
          contentType,
          title: title.substring(0, 70), // Jetstream only accepts 70 characters max
          filesize,
          originalFileName,
          organizationId: `hcorg:${organizationId}`,
          mediaUrl,
          attachToEpisodeId,
        },
      },
    };

    const response = await jetstreamClient.post(apiUrl, {
      json: query,
      headers,
    });

    const responseData = await response.json();
    const { data, errors = [] } = responseData;

    if (errors.length > 0) {
      Logger.error('Error creating jetstream media', errors);
      Logger.error(JSON.stringify(errors, null, 2));
      throw new Error('Error creating jetstream media');
    }

    return data?.createMedia;
  } catch (error) {
    Logger.error('Error creating jetstream media', error.response);
    return null;
  }
}

// Function to upload a file to jetstream using a signed url.
export async function uploadFileToJetstream(filePath, signedUrl) {
  const fileData = fs.readFileSync(filePath);
  const fileSize = fs.statSync(filePath).size;

  try {
    // Create a client for file uploads with longer timeout
    const uploadClient = createExternalApiClient({
      timeout: 300000, // 5 minutes for file uploads
    });

    const response = await uploadClient.put(signedUrl, {
      body: fileData,
      headers: {
        'Content-Length': fileSize,
        'Content-Type': 'application/octet-stream',
      },
    });

    if (!response.ok) {
      throw new Error('Upload failed');
    }

    return response.data;
  } catch (error) {
    Logger.error('Error uploading file to jetstream', error.response);
    return null;
  }
}
// Private function to generate a 5-digit code to create jetstream media.
/**
 * Function to generate a unique 5-digit code to be used in jetstream.
 * @param {Object} headers - The headers to be used in the ky request.
 * @param {String} apiUrl - The url to be used in the ky request.
 * @param {Number} counter - The number of times the function has been called recursively.
 * @returns {Promise<string>} A promise that resolves to a unique 5-digit code.
 */
export async function generateJetstreamCode({ headers, apiUrl, counter = 0 }) {
  const characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const codeLength = 5;
  let code = '';

  for (let i = 0; i < codeLength; i += 1) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    code += characters[randomIndex];
  }

  // We check if the code already exists in jetstream, if it does we generate a new one.

  const query = {
    operationName: 'IsUniqueEpisodeCode',
    query: `query IsUniqueEpisodeCode($input: IsUniqueEpisodeCodeInput!) {
      isUniqueEpisodeCode(input: $input)
    }`,
    variables: {
      input: {
        code,
      },
    },
  };

  const isUnique = await jetstreamClient.post(apiUrl, {
    json: query,
    headers,
  });

  const isUniqueResponse = await isUnique.json();
  const isUniqueCode = isUniqueResponse?.data?.isUniqueEpisodeCode || false;

  if (!isUniqueCode && counter < 10) {
    return generateJetstreamCode({ headers, apiUrl, counter: counter + 1 });
  }

  // If we tried 10 times and the code is not unique we return null.
  if (counter >= 10) {
    Logger.error('Error generating unique jetstream code');
    return null;
  }

  return code;
}

/**
 * Function to add the brand to the jetstream media.
 * @param {String} title
 * @param {String} organizationId
 * @param {String} code
 * @param {String} brandId
 */
export async function createJestreamEpisode({
  title,
  organizationId,
  code,
  brandId,
} = {}) {
  try {
    const accessToken = process.env.JETSTREAM_GRAPHQL_ACCESS_TOKEN;
    const apiUrl = process.env.JETSTREAM_GRAPHQL_API_URL;

    const headers = {
      'content-type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    };

    const query = {
      operationName: 'CreateEpisode',
      query: `mutation CreateEpisode($input: CreateEpisodeInput!) {
        createEpisode(input: $input) {
         id
        }
      }`,
      variables: {
        input: {
          brandId, // brandId should include the hcb: prefix
          code,
          organizationId: `hcorg:${organizationId}`,
          title,
        },
      },
    };

    const response = await jetstreamClient.post(apiUrl, {
      json: query,
      headers,
    });

    const responseData = await response.json();
    const { data, errors = [] } = responseData;

    if (errors.length > 0) {
      Logger.error('Error creating Jetstream Episode', errors);
      return null;
    }

    Logger.info('Jetstream Episode created', data);

    return data;
  } catch (error) {
    Logger.error('Error creating Jetstream Episode', error.response);
    Logger.error('Errors in data', error.response.data.errors);
    return null;
  }
}

// This function is to be called when the user uploads a file in the browser to google storage with the signed url and wants to create the episode in jetstream.
export async function createJetstreamUploadedEpisode({
  episode,
  show,
  channel,
  googleResponse,
}) {
  // To make this work we send when "create" is pressed the episode object with the title and the googleResponse from uploading to google storage with the signed url from the browser.
  // In this function we create the episode in jetstream and attach the media to it. We return the jetstream media url which will replace the mediaLinks.mp3.link in the episode object.
  const accessToken = process.env.JETSTREAM_GRAPHQL_ACCESS_TOKEN;
  const apiUrl = process.env.JETSTREAM_GRAPHQL_API_URL;

  const headers = {
    'content-type': 'application/json',
    'Authorization': `Bearer ${accessToken}`,
  };

  if (!googleResponse) {
    return { error: 'No googleResponse provided' }; // No media to upload
  }

  const { brandId } = show.jetstream || {};
  const { organizationId } = channel.jetstream || {};

  if (!brandId || !organizationId) {
    return { error: 'No brandId or organizationId provided' };
  }

  // Media url that is used to create the jetstream media. (For step 3)
  // Public URL
  const mediaUrl = `https://storage.googleapis.com/${googleResponse.bucket}/${googleResponse.name}`;

  // Code to be used in jetstream, it was required, they could generate this automatically in the future.
  const code = await generateJetstreamCode({ headers, apiUrl }); // TODO: This function should check if the code already exists in jetstream and generate a new one if it does.

  try {
    // We create the episode in jetstream under the brand of the show, returns the id of the episode.
    const jetstreamEpisode = await createJestreamEpisode({
      brandId,
      code,
      organizationId,
      title: episode.title,
    });

    const jetstreamEpisodeId = jetstreamEpisode?.createEpisode?.id || null;

    // If the selfLink ends in .mp3 we know it's an audio file, else it should be a video file.
    const contentType = googleResponse.selfLink.endsWith('.mp3')
      ? 'AUDIO'
      : 'VIDEO';

    // console.log('contentType', contentType);

    // Step 3: We create the jetstream media.
    const media = await createJetstreamMedia({
      contentType,
      title: episode.title,
      originalFileName: episode._id,
      filesize: googleResponse.size,
      mediaUrl,
      organizationId,
      code,
      attachToEpisodeId: jetstreamEpisodeId || null, // If episode was created, we attach the media to it.
    });

    // mediaUrl we get (master) https://eu.jstre.am/audios/jsa:5OYPtYS87q9/master/64c0d81a67cbf973044f5de6.mp3
    // Example of aac url       https://eu.jstre.am/audios/jsa:5OYPtYS87q9/aac/64c0d81a67cbf973044f5de6.m4a
    const tempLinks =
      contentType === 'AUDIO'
        ? {
            'jetstream': {}, // Make sure the jetstream video is removed
            'jetstream-audio': {
              link: media.id,
            },
          }
        : {
            'jetstream-audio': {}, // Make sure the jetstream audio is removed
            'jetstream': {
              link: media.id, // Remove the jsv: prefix
            },
          };

    // We update the episode with the new media link.
    const updatedEpisode = await Episode.findByIdAndUpdate(
      episode._id,
      {
        processed: false, // We want to keep track of when it's done processing.
        timesProcessed: 0, // We reset the times processed.
        tempLinks,
      },
      {
        new: true,
        runValidators: true,
      }
    );

    // If the episode is a video, we run createOrUpdateMediaLinks
    // TODO: change this to run when finished processing.
    // if (contentType === 'VIDEO') {
    //   await Episode.createOrUpdateMediaLinks(
    //     { body: { mediaLinks } }, // This contains the jetstream object with the id
    //     updatedEpisode,
    //     channel
    //   );
    // }

    Logger.info(
      'Jetstream Episode created',
      jetstreamEpisodeId,
      'with media id',
      media.id
    );
    return updatedEpisode;
  } catch (error) {
    return { error: `Error creating Jetstream Episode: ${error}` };
  }
}

export async function getJetstreamProcessedPercentage(tempLinks) {
  // tempLinks is an object which can have either jetstream or jetstream-audio as keys.
  const accessToken = process.env.JETSTREAM_GRAPHQL_ACCESS_TOKEN;
  const apiUrl = process.env.JETSTREAM_GRAPHQL_API_URL;

  const { jetstream, 'jetstream-audio': jetstreamAudio } = tempLinks || {};
  // We get the correct prefix for the media type.
  const jetstreamId = jetstream?.link
    ? getJetstreamId(jetstream?.link)
    : getJetstreamId(jetstreamAudio?.link, 'audio'); // If it's an audio file we use the audio prefix of jsa

  if (!jetstreamId) {
    return null;
  }

  // Create Jetstream GraphQL client
  const jetstreamClient = createExternalApiClient({
    timeout: 30000,
  });

  const headers = {
    'content-type': 'application/json',
    'Authorization': `Bearer ${accessToken}`,
  };

  const query = {
    operationName: 'Media',
    query: `query Media($mediaId: String!) {
      media(id: $mediaId) {
        processingWorkflowRun {
          progress
          status
        }
      }
    }`,
    variables: {
      mediaId: jetstreamId,
    },
  };

  const response = await jetstreamClient.post(apiUrl, {
    json: query,
    headers,
  });

  const responseData = await response.json();
  const { progress: percentage, status } = responseData?.data?.media
    ?.processingWorkflowRun || { progress: 0, status: 'PENDING' };

  const progress = {
    status,
    percentage,
  };
  return progress;
}

export async function completeJetstreamProcessing(req, episode) {
  // Set tempLinks as mediaLinks and processed to true.
  const { tempLinks: mediaLinks } = episode;
  req.body.mediaLinks = mediaLinks || {};

  // Channel is not passed since only vimeo uses it.
  const { body: updatedBody, error: mediaLinksError } =
    await createOrUpdateMediaLinks({
      body: req.body,
      episode,
      entity: req.entity,
    }); // Channel is not passed since only vimeo uses it.

  if (mediaLinksError) {
    throw new Error('Error creating or updating media links');
  }

  // After updating the mediaLinks we set the episode as processed.
  await Episode.findByIdAndUpdate(
    episode._id,
    {
      ...updatedBody,
      processed: true,
    },
    {
      new: true,
      runValidators: true,
    }
  );
}

/**
 * Retrieves the processing progress of a specific episode from Jetstream.
 *
 * @param {Object} req - The request object.
 * @param {String} episodeId - The ID of the episode to retrieve the progress for.
 * @returns {Promise<Object>} A promise that resolves to an object containing the status and percentage of the processing progress.
 * The status can be 'DONE' if the episode was already processed, 'ERROR' if there was an error, or the current status of the processing.
 * The percentage is 100 if the episode was already processed, 0 if there was an error, or the current percentage of the processing.
 * If the episode is fully processed during the function call, the episode is updated to reflect this.
 */
export async function getJetstreamEpisodeProgress(req, episodeId) {
  const episode = await Episode.findById(episodeId, 'processed tempLinks');

  // Check if episode was already processed
  if (episode.processed) {
    return { status: 'DONE', percentage: 100 };
  }

  // Check if episode has tempLinks, and get the progress from jetstream
  if (episode.tempLinks) {
    const progress = await getJetstreamProcessedPercentage(episode.tempLinks);

    const { percentage, status } = progress || {};
    Logger.info(`Jetstream processing progress: ${percentage}% (${status})`);

    // If the episode is processed, update the episode and return the progress
    if (percentage >= 100 || status === 'DONE') {
      // Set processed and medialinks
      await completeJetstreamProcessing(req, episode.toObject()); // req is used in createOrUpdateMediaLinks
    }
    return progress;
  }

  return { status: 'ERROR', percentage: 0 };
}

export default {
  getJetstreamId,
  getMediaLink,
  getRendition,
  getDuration,
  getVideo,
  getMedia,
  getVideoEventsAggregate,
  getVideos,
  getMediaUploadUrl,
  createJetstreamMedia,
  uploadFileToJetstream,
  generateJetstreamCode,
  createJestreamEpisode,
  createJetstreamUploadedEpisode,
  getJetstreamProcessedPercentage,
  completeJetstreamProcessing,
  getJetstreamEpisodeProgress,
};
