import { handleKyError } from './kyErrorHandler.js';
import { describe, test, expect } from 'vitest';

// Mock error response
const mockErrorResponse = (status, json) => {
  return {
    response: {
      status,
      json: () => Promise.resolve(json),
      text: () => Promise.resolve(JSON.stringify(json))
    }
  };
};

describe('handleKyError', () => {
  test('should normalize HTTPError responses with JSON', async () => {
    const err = mockErrorResponse(404, { message: 'Not Found', code: 'NOT_FOUND' });
    const result = await handleKyError(err, { action: 'testing' });
    expect(result).toEqual({
      status: 404,
      code: 'NOT_FOUND',
      message: 'Not Found',
      data: { message: 'Not Found', code: 'NOT_FOUND' },
      context: { action: 'testing' }
    });
  });

  test('should handle non-HTTP errors', async () => {
    const err = new Error('Network error');
    const result = await handleKyError(err, 'initializing');
    expect(result).toEqual({
      status: 0,
      code: 'Error',
      message: 'Network error',
      data: { stack: err.stack },
      context: 'initializing'
    });
  });

  test('should fallback to text response when JSON parsing fails', async () => {
    const err = {
      response: {
        status: 500,
        json: () => Promise.reject(new Error('Invalid JSON')),
        text: () => Promise.resolve('Internal Server Error')
      }
    };
    const result = await handleKyError(err);
    expect(result.status).toBe(500);
    expect(result.code).toBe('HTTP_500');
    expect(result.message).toBe('Internal Server Error');
    expect(result.context).toBe(null);
  });

  test('should handle errors with no context', async () => {
    const err = new Error('Simple error');
    const result = await handleKyError(err);
    expect(result.context).toBe(null);
    expect(result.message).toBe('Simple error');
  });

  test('should handle null/undefined errors gracefully', async () => {
    const result = await handleKyError(null);
    expect(result.status).toBe(500);
    expect(result.code).toBe('UNKNOWN_ERROR');
    expect(result.message).toBe('An unknown error occurred');
  });
});

