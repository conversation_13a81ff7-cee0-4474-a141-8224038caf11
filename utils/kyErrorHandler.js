import { logError } from '#utils/logger.js';

/**
 * Handles ky HTTP errors and normalizes them into a standard format
 * @param {Error} err - The error object, potentially a ky.HTTPError instance
 * @param {string|Object} [context] - Additional context information for the error
 * @returns {Promise<Object>} A normalized error object with status, code, message, data, and context
 * @throws {Object} The normalized error object
 *
 * @example
 * try {
 *   const response = await ky.get('https://api.example.com/data');
 * } catch (error) {
 *   const normalizedError = await handleKyError(error, 'fetching user data');
 *   throw normalizedError;
 * }
 */
export async function handleKyError(err, context) {
  let status = 500;
  let code = 'UNKNOWN_ERROR';
  let message = 'An unknown error occurred';
  let data = null;

  // Detect ky.HTTPError instances
  const isKyHTTPError =
    err && err.response && typeof err.response.json === 'function';

  if (isKyHTTPError) {
    // It's a ky HTTPError, extract response data
    const { status: responseStatus } = err.response;
    status = responseStatus;

    try {
      // Try to parse JSON response first
      const responseData = await err.response.json();
      message =
        responseData.message ||
        responseData.error ||
        err.message ||
        `HTTP ${status} Error`;
      code = responseData.code || responseData.error_code || `HTTP_${status}`;
      data = responseData;
    } catch (_jsonError) {
      try {
        // Fallback to text response
        const responseText = await err.response.text();
        message = responseText || err.message || `HTTP ${status} Error`;
        code = `HTTP_${status}`;
        data = { responseText };
      } catch (_textError) {
        // Final fallback
        message = err.message || `HTTP ${status} Error`;
        code = `HTTP_${status}`;
        data = { statusText: err.response.statusText };
      }
    }
  } else if (err) {
    // Handle other error types (network errors, etc.)
    message = err.message || 'Network or unknown error';
    code = err.code || err.name || 'NETWORK_ERROR';
    status = err.status || 0;

    // Capture stack trace if available
    if (err.stack) {
      data = { stack: err.stack };
    }
  }

  // Create normalized error object
  const normalizedError = {
    status,
    code,
    message,
    data,
    context: context || null,
  };

  // Log error in non-production environments (safe mode)
  if (process.env.NODE_ENV !== 'production') {
    const contextStr = context
      ? ` [Context: ${typeof context === 'string' ? context : JSON.stringify(context)}]`
      : '';
    logError(`Ky Error Handler: ${code} - ${message}${contextStr}`, {
      status,
      code,
      originalError: err?.message || 'No original error message',
      context,
    });
  }

  return normalizedError;
}
