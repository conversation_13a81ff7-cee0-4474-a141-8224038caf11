import Logger from '#utils/logger.js';
import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';

// Create a shared client for WordPress API calls
const wpClient = createExternalApiClient();

export async function getPosts(apiUrl, params = {}) {
  try {
    if (!apiUrl) {
      return [];
    }
    const query = new URLSearchParams(params);
    const response = await wpClient.get(`${apiUrl}/posts?${query.toString()}`);
    const data = await response.json();
    const headers = Object.fromEntries(response.headers.entries());
    return { posts: data, headers };
  } catch (error) {
    const normalizedError = await handleKyError(error, 'wordpress-posts');
    Logger.error(
      `Error querying WordPress posts with params ${JSON.stringify(params)}`,
      normalizedError
    );
    return [];
  }
}

export async function getCategories(apiUrl, params = { per_page: 100 }) {
  try {
    if (!apiUrl) {
      return [];
    }
    const query = new URLSearchParams(params);
    const data = await wpClient
      .get(`${apiUrl}/categories?${query.toString()}`)
      .json();
    return data;
  } catch (error) {
    const normalizedError = await handleKyError(error, 'wordpress-categories');
    Logger.error(
      `Error querying WordPress categories with params ${JSON.stringify(
        params
      )}`,
      normalizedError
    );
    return [];
  }
}

export async function getTags(apiUrl, params = { per_page: 100 }) {
  try {
    if (!apiUrl) {
      return [];
    }
    const query = new URLSearchParams(params);
    const data = await wpClient
      .get(`${apiUrl}/tags?${query.toString()}`)
      .json();
    return data;
  } catch (error) {
    const normalizedError = await handleKyError(error, 'wordpress-tags');
    Logger.error(
      `Error querying WordPress tags with params ${JSON.stringify(params)}`,
      normalizedError
    );
    return [];
  }
}

export async function getImage(apiUrl, mediaId) {
  try {
    const data = await wpClient.get(`${apiUrl}/media/${mediaId}`).json();
    return data;
  } catch (error) {
    const normalizedError = await handleKyError(error, 'wordpress-media');
    Logger.error(`Error querying WordPress media ${mediaId}`, normalizedError);
    return null;
  }
}
