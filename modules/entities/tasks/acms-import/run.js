import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';

import personRoleService from '#modules/persons/services/personRoleService.js';
import personService from '#modules/persons/services/personService.js';
import { toObjectId } from '#utils/api/mongoose/id.js';
import { toUnixDate } from '#utils/dates.js';
import Logger from '#utils/logger.js';

import entityServices from '../../services/entityServices.js';
import serviceServices from '../../services/serviceServices.js';
import { convertACMSEntityToEntity } from './helpers/convertACMSEntityToEntity.js';
import { convertACMSFeaturesToEntityAmenities } from './helpers/convertACMSFeaturesToEntityAmenities.js';
import { convertACMSParentEntityToEntity } from './helpers/convertACMSParentEntityToEntity.js';
import { convertACMSPersonToPerson } from './helpers/convertACMSPersonToPerson.js';
import { convertACMSServiceToEntityService } from './helpers/convertACMSServiceToEntityService.js';

const IMPORT_PATH = '/Membership/V1/Church';

async function createOrUpdateParentEntity({
  networkId,
  targetEntityId,
  acmsEntity,
}) {
  // We first check whether the parent entity exists as an OrgMast entity.
  const existingOrgMastEntity = await entityServices.getEntityByImportId(
    'orgmast',
    acmsEntity.ParentEntity.Code
  );

  if (existingOrgMastEntity) {
    Logger.info(
      `Using existing OrgMast parent entity ${acmsEntity.ParentEntity.Name} with external UID ${acmsEntity.ParentEntity.Code} and local ID ${existingOrgMastEntity._id}`
    );

    return existingOrgMastEntity;
  }

  // If the parent entity does not exist as an OrgMast entity, we check whether it exists as an ACMS entity.
  let existingACMSParentEntity = await entityServices.getEntityByImportId(
    'acms',
    acmsEntity.ParentEntity.Id
  );

  if (existingACMSParentEntity) {
    Logger.info(
      `Using existing ACMS parent entity ${acmsEntity.ParentEntity.Name} with external UID ${acmsEntity.ParentEntity.Id} and local ID ${existingACMSParentEntity._id}`
    );
    existingACMSParentEntity = await entityServices.updateEntity(
      existingACMSParentEntity._id,
      convertACMSParentEntityToEntity({
        networkId,
        targetEntityId,
        acmsParentEntity: acmsEntity.ParentEntity,
      })
    );
  } else {
    Logger.info(
      `Creating new parent entity ${acmsEntity.ParentEntity.Name} for ${acmsEntity.Name} (${acmsEntity.Id}) with external UID ${acmsEntity.ParentEntity.Id}`
    );
    existingACMSParentEntity = await entityServices.createEntity(
      convertACMSParentEntityToEntity({
        networkId,
        targetEntityId,
        acmsParentEntity: acmsEntity.ParentEntity,
      })
    );
    Logger.info(
      `Using created parent entity ${acmsEntity.ParentEntity.Name} with external UID ${acmsEntity.ParentEntity.Id} and local ID ${existingACMSParentEntity._id}`
    );
  }

  return existingACMSParentEntity;
}

async function createOrUpdateEntity({
  targetEntity,
  networkId,
  parentEntity,
  acmsEntity,
  amenities,
}) {
  let existingEntity = await entityServices.getEntityByImportId(
    'acms',
    acmsEntity.Id
  );

  if (existingEntity) {
    Logger.info(
      `  Updating existing entity ${acmsEntity.Name} with external UID ${acmsEntity.Id} and local ID ${existingEntity._id}`
    );

    entityServices.updateEntity(existingEntity._id, {
      ...convertACMSEntityToEntity({
        targetEntity,
        networkId,
        parentEntity,
        acmsEntity,
      }),
      amenities,
      // NOTE: At this point we have filtered out entities that are not active in ACMS. This is why we can safely set `deleted` to false and `enabled` to true, in case they were previously not set.
      deleted: false,
      enabled: true,
    });
  } else {
    Logger.info(
      `  Creating new entity ${acmsEntity.Name} with external UID ${acmsEntity.Id}`
    );

    existingEntity = await entityServices.createEntity({
      ...convertACMSEntityToEntity({
        targetEntity,
        networkId,
        parentEntity,
        acmsEntity,
      }),
      amenities,
    });

    // NOTE: Some information (such as location) is not added on entity creation, so we update the entity again to ensure all information is present.
    existingEntity = await entityServices.updateEntity(existingEntity._id, {
      ...convertACMSEntityToEntity({
        targetEntity,
        networkId,
        parentEntity,
        acmsEntity,
      }),
      amenities,
    });
  }

  return existingEntity;
}

async function importEntity(targetEntity, acmsEntity) {
  const targetEntityId = targetEntity._id;
  const networkId = targetEntity.network._id;

  // ACMS feature with ID 72a75515-4f9e-4405-ae2d-ea9f5f791325 is used to indicate if the entity is active. It is returned here as the `entityAvailable` boolean.
  const { entityAvailable, amenities } = convertACMSFeaturesToEntityAmenities(
    acmsEntity.Features
  );

  // Skip to next entity if current entity is not active and doesn't have a city
  if (
    !acmsEntity.Active ||
    // !acmsEntity.StreetAddress?.City || // TODO: Is this check necessary?
    !entityAvailable
  ) {
    return;
  }

  const existingParentEntity = await createOrUpdateParentEntity({
    networkId,
    targetEntityId,
    acmsEntity,
  });

  const existingEntity = await createOrUpdateEntity({
    targetEntity,
    networkId,
    parentEntity: existingParentEntity,
    acmsEntity,
    amenities,
  });

  return existingEntity;
}

async function importPeopleRoles(targetEntity, personRoleEntityId, acmsPeople) {
  // At this point, we have already imported the people for this entity, so we can get their local IDs.
  const existingImportedPersons = acmsPeople?.length
    ? await personService.getImportedPersonIds({
        entityId: targetEntity._id,
        type: 'acms',
        recordIds: acmsPeople.map((person) => person.Id),
      })
    : [];

  const existingPersonRoles = await personRoleService.getPersonRolesByPersonIds(
    {
      personIds: existingImportedPersons.map((person) => person._id),
      entityId: personRoleEntityId,
    }
  );

  const personRolesToDelete = [
    ...existingPersonRoles.map((personRole) => personRole._id),
  ];

  const acmsContactPerson =
    acmsPeople.find((acmsPerson) => acmsPerson.IsMainPastor) || acmsPeople[0];

  for (const person of existingImportedPersons) {
    let existingPersonRole = await personRoleService.getPersonRoleByPersonId({
      personId: person._id,
      entityId: personRoleEntityId,
    });

    if (existingPersonRole) {
      Logger.info(
        `    Updating existing person role for person with ID ${person._id} in entity ${personRoleEntityId} to pastor`
      );

      existingPersonRole = await personRoleService.updatePersonRole({
        roleId: existingPersonRole._id,
        entityId: personRoleEntityId,
        personRoleData: { role: 'pastor' },
      });
    } else {
      Logger.info(
        `    Creating new person role for person with ID ${person._id} in entity ${personRoleEntityId} as pastor`
      );
      existingPersonRole = await personRoleService.createPersonRole({
        entityId: personRoleEntityId,
        personId: person._id,
        personRoleData: { role: 'pastor' },
      });
    }

    if (existingPersonRole) {
      const personImportId = person.importIDs?.[0]?.recordID;
      // If the person is the main pastor or contact person, we add them as a contact person for the entity.
      if (personImportId === acmsContactPerson?.Id) {
        Logger.info(
          `    Adding person with ID ${person._id} to entity ${personRoleEntityId} as a contact`
        );
        await entityServices.addContactPerson({
          entityId: personRoleEntityId,
          personRoleId: existingPersonRole._id,
        });
      }

      personRolesToDelete.splice(
        personRolesToDelete.indexOf(existingPersonRole._id),
        1
      );
    }
  }

  if (personRolesToDelete.length > 0) {
    Logger.info(`    Deleting ${personRolesToDelete.length} person roles`);
    const deletedPersonRoleCount =
      await personRoleService.deletePersonRolesByPersonIds({
        personIds: personRolesToDelete.map((personRole) => personRole._id),
        entityId: personRoleEntityId,
      });
    Logger.info(`    Deleted ${deletedPersonRoleCount} person roles`);
  }
}

async function importPeople(targetEntity, personRoleEntityId, acmsPeople) {
  const people = [];

  for (const acmsPerson of acmsPeople) {
    let existingPerson = await personService.getPersonByImportId(
      'acms',
      acmsPerson.Id
    );

    const person = await convertACMSPersonToPerson(
      targetEntity,
      acmsPerson,
      existingPerson
    );

    if (existingPerson) {
      Logger.info(
        `    Updating existing person ${acmsPerson.Name} with external UID ${acmsPerson.Id} and local ID ${existingPerson._id}`
      );

      existingPerson = await personService.updatePerson({
        personId: existingPerson._id,
        personData: person,
        entityId: targetEntity._id,
        personRoleEntityId,
      });
    } else {
      Logger.info(
        `    Creating new person ${acmsPerson.Name} with external UID ${acmsPerson.Id}`
      );
      existingPerson = await personService.createPerson({
        entityId: targetEntity._id,
        personData: person,
      });
    }

    if (existingPerson) {
      people.push(existingPerson._id.toString());
    }
  }

  return people;
}

async function importServices(targetEntity, serviceEntityId, acmsServices) {
  const existingImportedServices = await serviceServices.getImportedServiceIds({
    entityId: serviceEntityId,
    type: 'acms',
  });
  const importedServicesToDelete = [...existingImportedServices];

  for (const acmsService of acmsServices) {
    let existingService = await serviceServices.getServiceByImportId(
      'acms',
      acmsService.Id
    );

    const service = convertACMSServiceToEntityService(
      acmsService,
      targetEntity.language || 'en'
    );

    if (existingService) {
      Logger.info(
        `    Updating existing service ${acmsService.Name} with external UID ${acmsService.Id} and local ID ${existingService._id}`
      );
      existingService = await serviceServices.updateService(
        existingService._id,
        service
      );
    } else {
      Logger.info(
        `    Creating new service ${acmsService.Name} with external UID ${acmsService.Id}`
      );
      existingService = await serviceServices.createService(
        serviceEntityId,
        service
      );
    }

    if (existingService) {
      importedServicesToDelete.splice(
        importedServicesToDelete.indexOf(acmsService.Id),
        1
      );
    }
  }

  if (importedServicesToDelete.length > 0) {
    Logger.info(`    Deleting ${importedServicesToDelete.length} services`);
    const deletedServiceCount = await serviceServices.deleteServicesByIds({
      serviceIds: importedServicesToDelete.map((service) => service._id),
      entityId: serviceEntityId,
    });
    Logger.info(`    Deleted ${deletedServiceCount} services`);
  }
}

async function importACMSData(targetEntity, lastSync = 0) {
  if (!targetEntity || !targetEntity?.network?._id) {
    Logger.error(`Entity or Entity Network not provided`);
    return [];
  }

  try {
    const query = new URLSearchParams({ abbreviation: 'buc' });

    // Create ACMS API client
    const acmsClient = createExternalApiClient({
      prefixUrl: process.env.ACMS_API_URL,
      headers: {
        'app-key': process.env.ACMS_API_KEY,
      },
      timeout: 30000, // 30 seconds timeout for ACMS API
    });

    const acmsEntities = await acmsClient
      .get(`${IMPORT_PATH}?${query.toString()}`)
      .json();

    const acmsEntitiesToImport = acmsEntities.filter((acmsEntity) => {
      const lastModified = toUnixDate(acmsEntity.LastUpdateDate);

      return lastModified > lastSync;
    });

    if (acmsEntitiesToImport.length === 0) {
      Logger.info(`No entities to import since last sync`);
      return [];
    }

    const existingImportedEntities = await entityServices.getImportedEntityIds({
      parentEntityId: targetEntity._id,
      type: 'acms',
    });
    let importedEntitiesToDelete = [...existingImportedEntities];

    const existingImportedPersons = await personService.getImportedPersonIds({
      entityId: targetEntity._id,
      type: 'acms',
    });
    let importedPersonsToDelete = [
      ...existingImportedPersons.map((person) => person._id),
    ];

    Logger.info(`Importing ${acmsEntitiesToImport.length} entities`);

    for (const acmsEntity of acmsEntitiesToImport) {
      const existingEntity = await importEntity(targetEntity, acmsEntity);

      if (existingEntity) {
        importedEntitiesToDelete = importedEntitiesToDelete.filter(
          ({ importIDs }) =>
            !importIDs?.some(({ recordID }) => recordID === acmsEntity.Id)
        );
      }

      // Skip to next entity if current entity is not returned
      if (!existingEntity) {
        continue;
      }

      // Import people
      const importedPeople = await importPeople(
        targetEntity,
        existingEntity._id,
        acmsEntity.Pastors
      );

      // Remove people from delete list
      if (importedPeople.length) {
        importedPersonsToDelete = importedPersonsToDelete.filter(
          (person) => !importedPeople.includes(person._id.toString())
        );
      }

      // Import people roles
      await importPeopleRoles(
        targetEntity,
        existingEntity._id,
        acmsEntity.Pastors
      );

      // Import services
      await importServices(
        targetEntity,
        existingEntity._id,
        acmsEntity.ServiceTimes
      );
    }

    if (importedPersonsToDelete.length > 0) {
      Logger.info(
        `    Deleting ${importedPersonsToDelete.length} ${importedPersonsToDelete.length === 1 ? 'person' : 'people'}`
      );
      const deletedPersonCount = await personService.deletePeopleByIds({
        personIds: importedPersonsToDelete.map((person) => person._id),
      });
      Logger.info(
        `    Deleted ${deletedPersonCount} ${importedPersonsToDelete.length === 1 ? 'person' : 'people'}`
      );
    }

    if (importedEntitiesToDelete.length > 0) {
      Logger.info(`Deleting ${importedEntitiesToDelete.length} entities`);
      const deletedEntityCount = await entityServices.deleteEntitiesByIds(
        importedEntitiesToDelete.map((entity) => toObjectId(entity._id))
      );
      Logger.info(`Deleted ${deletedEntityCount} entities`);
    }
  } catch (error) {
    const normalizedError = await handleKyError(
      error,
      `acms-import-${IMPORT_PATH}`
    );
    Logger.error(
      `Error querying ACMS with path ${IMPORT_PATH}`,
      normalizedError
    );
    return [];
  }
}

export default async function runAcmsImport(task) {
  const { settings } = task ?? {};
  const { importAll, entity: entityId } = settings ?? {};

  const entity = await entityServices.getEntityById(entityId, {
    populate: ['network'],
  });
  const lastSync = importAll ? 0 : toUnixDate(task.lastSync);

  const results = await importACMSData(entity, lastSync);

  return results;
}
