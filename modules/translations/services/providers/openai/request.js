import { logError } from '#utils/logger.js';
import { createExternalApiClient } from '#utils/kyClient.js';

/**
 * @typedef {'gpt-4.1'|'gpt-4.1-mini'|'gpt-4o'|'gpt-4o-mini'|'gpt-4'|'gpt-3.5-turbo'} OpenAIModel An OpenAI model to use for chat completion
 */

/**
 * @typedef {Object} OpenAIModelSettings Parameters to configure the chat completion request
 * @property {Number} [totalCompletions=1] Number of completions to generate between 1 and 2048
 * @property {Number} [temperature=0.0] The higher the temperature, the crazier the text
 * @property {Number} maxOutputTokens The maximum number of tokens to generate
 * @property {Number} [timeout=9000] The timeout for the request in milliseconds
 */

/**
 * @typedef {Object} OpenAIResponse
 * @property {String} translatedText The translated text
 * @property {Error} error The error message
 */

const url = 'https://api.openai.com/v1/chat/completions';

// Default settings for OpenAI model requests
const defaultModelSettings = {
  totalCompletions: 1, // Number of completions to generate (limited to 1 for now)
  temperature: 0.0, // The higher the temperature, the crazier the text
  maxOutputTokens: 4096, // The maximum number of tokens to generate
  timeout: 90000, // 90 seconds timeout for the request
};

/**
 * OpenAI model settings
 * @type {Record<OpenAIModel, OpenAIModelSettings>}
 */
export const openAIModels = {
  'gpt-4o': {
    contextWindow: 128_000,
    maxOutputTokens: 16_384,
    cost: 'medium',
    quality: 'best',
    description:
      'Best general-purpose model for high-quality translation across many languages, especially with idiomatic or nuanced content.',
  },
  'gpt-4o-mini': {
    contextWindow: 128_000,
    maxOutputTokens: 16_384,
    cost: 'cheap',
    quality: 'good',
    description:
      'Cost-efficient alternative to GPT-4o with solid translation quality for most language pairs and faster response times.',
  },
  'gpt-4.1-mini': {
    contextWindow: 1_000_000,
    maxOutputTokens: 4096,
    cost: 'medium',
    quality: 'best',
    description:
      'Ideal for translating long documents with consistent terminology and handling very large contexts.',
  },
  'o3': {
    contextWindow: 200_000,
    maxOutputTokens: 100_000,
    cost: 'expensive',
    quality: 'best',
    description:
      'High-end model for professional translation pipelines where quality, structure preservation, and large context are critical.',
  },
  'o3-mini': {
    contextWindow: 200_000,
    maxOutputTokens: 100_000,
    cost: 'medium',
    quality: 'good',
    description:
      'Cheaper version of o3; good balance of quality and cost for structured multilingual content.',
  },
  'o4-mini': {
    contextWindow: 200_000,
    maxOutputTokens: 100_000,
    cost: 'medium',
    quality: 'good',
    description:
      'Modern and efficient model, great for translation at scale with good context awareness and stable quality.',
  },
};

/**
 * Get the model settings for the given model
 * @param {OpenAIModel} model The model to get the settings for
 * @returns {OpenAIModelSettings} The model settings
 */
function getModelSettings(model) {
  return {
    ...defaultModelSettings, // Base settings
    ...(openAIModels[model] || {}), // Specific settings for the model
  };
}

/**
 * A base helper to prompt OpenAI to do some text completion
 * @param {Object} params The parameters object
 * @param {String} params.prompt The prompt to generate completions for
 * @param {Object} params.params Additional parameters to pass to the OpenAI API (see https://platform.openai.com/docs/api-reference/completions/create)
 * @param {OpenAIModel} [params.model='gpt-4.1'] The OpenAI model to use for the translation (see https://platform.openai.com/docs/models/model-endpoint-compatibility)
 * @param {String} params.apiKey The OpenAI API key to use
 * @returns {Promise<OpenAIResponse>} The translated text or an error
 */
export default async function getOpenAIRequest({
  prompt = '',
  params = {},
  model = 'gpt-4.1',
  apiKey,
}) {
  // Check if the API key is missing
  if (!apiKey) {
    return {
      error: 'OpenAI API key is missing',
    };
  }

  if (!prompt) {
    return {
      error: 'Prompt is missing',
    };
  }

  if (!model) {
    return {
      error: 'Model is missing',
    };
  }

  // Get the model settings (or use the default settings as a fallback)
  const { timeout, maxOutputTokens, totalCompletions, temperature } =
    getModelSettings(model);

  // Create a Ky instance with a custom timeout
  const client = createExternalApiClient({ timeout });

  try {
    const data = await client
      .post(url, {
        json: {
          ...params, // Additional parameters to pass to the OpenAI API
          max_tokens: maxOutputTokens, // The maximum number of tokens to generate // TODO: Add tiktoknizer to calculate the number of tokens
          n: totalCompletions, // Number of completions to generate
          temperature: temperature, // The higher the temperature, the crazier the text
          model, // The model to use for completion
          messages: [{ role: 'user', content: prompt }], // The prompt to generate completions for
        },
        headers: {
          Authorization: `Bearer ${apiKey}`, // Set the API key in the Authorization header
        },
      })
      .json();

    // Extract the translated text from the response
    const { message } = data?.choices?.[0] || {};

    // Remove trailing spaces and quotes surounding from the text
    const translatedText =
      message?.content?.trim()?.replace(/^"(.*)"$/, '$1') || '';

    return { translatedText };
  } catch (error) {
    logError(error.response?.data || error.message);
    return {
      error,
    };
  }
}
