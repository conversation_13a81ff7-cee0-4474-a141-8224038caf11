import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';

/**
 * @typedef {'gpt-4.1'|'gpt-4.1-mini'|'gpt-4o'|'gpt-4o-mini'|'gpt-4'|'gpt-3.5-turbo'} OpenAIModel An OpenAI model to use for chat completion
 */

/**
 * @typedef {Object} OpenAIModelSettings Parameters to configure the chat completion request
 * @property {Number} [totalCompletions=1] Number of completions to generate between 1 and 2048
 * @property {Number} [temperature=0.0] The higher the temperature, the crazier the text
 * @property {Number} maxTokens The maximum number of tokens to generate
 * @property {Number} [timeout=9000] The timeout for the request in milliseconds
 */

/**
 * @typedef {Object} OpenAIResponse
 * @property {String} translatedText The translated text
 * @property {Error} error The error message
 */

// The OpenAI API URL for chat completions
const url = 'https://api.openai.com/v1/chat/completions';

/**
 * OpenAI model settings
 * @type {{[modelName: OpenAIModel]: OpenAIModelSettings}}
 */
const openAIModelSettings = {
  // Base settings for all models:
  'default': {
    totalCompletions: 1, // Number of completions to generate (limited to 1 for now)
    temperature: 0.0, // The higher the temperature, the crazier the text
    maxTokens: 4096, // The maximum number of tokens to generate
    timeout: 90000, // 90 seconds timeout for the request
  },

  // Specific settings for each model:
  'gpt-4.1': {
    maxTokens: 32768,
  },
  'gpt-4.1-mini': {
    maxTokens: 32768,
  },
  'gpt-4o': {
    maxTokens: 16384,
  },
  'gpt-4o-mini': {
    maxTokens: 16384,
  },
  'gpt-4': {
    maxTokens: 4096,
  },
  'gpt-3.5-turbo': {
    maxTokens: 4096,
  },
};

/**
 * Get the model settings for the given model
 * @param {OpenAIModel} model The model to get the settings for
 * @returns {OpenAIModelSettings} The model settings
 */
function getModelSettings(model) {
  return {
    ...openAIModelSettings.default, // Base settings
    ...(openAIModelSettings[model] || {}), // Specific settings for the model
  };
}

/**
 * A base helper to prompt OpenAI to do some text completion
 * @param {Object} params The parameters object
 * @param {String} params.apiKey The OpenAI API key to use
 * @param {OpenAIModel} [params.model] The OpenAI model to use for the translation (see https://platform.openai.com/docs/models/model-endpoint-compatibility)
 * @param {Object} [params.params={}] Additional parameters to pass to the OpenAI API (see https://platform.openai.com/docs/api-reference/completions/create)
 * @param {String} [params.prompt=''] The prompt to generate completions for
 * @returns {Promise<OpenAIResponse>} The translated text or an error
 */
export async function getOpenAIRequest({
  prompt = '',
  params = {},
  model,
  apiKey,
}) {
  // Check if the API key is missing
  if (!apiKey) {
    return {
      error: 'OpenAI API key is missing',
    };
  }

  if (!prompt) {
    return {
      error: 'Prompt is missing',
    };
  }

  if (!model) {
    return {
      error: 'Model is missing',
    };
  }

  // Get the model settings (or use the default settings as a fallback)
  const { timeout, maxTokens, totalCompletions, temperature } =
    getModelSettings(model);

  // Create a Ky instance with a custom timeout
  const client = createExternalApiClient({ timeout: timeout || 20000 });

  try {
    const data = await client
      .post(url, {
        json: {
          ...params, // Additional parameters to pass to the OpenAI API
          max_tokens: maxTokens, // The maximum number of tokens to generate
          n: totalCompletions, // Number of completions to generate
          temperature: temperature, // The higher the temperature, the crazier the text
          model, // The model to use for completion
          messages: [{ role: 'user', content: prompt }], // The prompt to generate completions for
        },
        headers: {
          Authorization: `Bearer ${apiKey}`, // Set the API key in the Authorization header
        },
      })
      .json();

    // Extract the translated text from the response
    const { message } = data?.choices?.[0] || {};

    // Remove trailing spaces and quotes surounding from the text
    const content = message?.content?.trim()?.replace(/^"(.*)"$/, '$1');

    return { content };
  } catch (error) {
    const normalizedError = await handleKyError(error, 'openai-completion');
    return {
      error: normalizedError,
    };
  }
}

/**
 * Get a response from an OpenAI request in JSON format
 * @param {Object} params The parameters object
 * @param {String} params.prompt The prompt to generate completions for
 * @param {OpenAIModel} [params.model] The OpenAI model to use for the translation (see https://platform.openai.com/docs/models/model-endpoint-compatibility)
 * @returns {Promise<Object>} The parsed JSON response
 */
export async function getJSONOpenAIRequest({ prompt, model }) {
  // Extract the categories from the title, abstract and tags using OpenAI
  const { content } = await getOpenAIRequest({
    prompt,
    model,
    params: {
      response_format: { type: 'json_object' }, // Ensure the response is a JSON object
    },
    apiKey: process.env.OPENAI_API_KEY,
  });

  //  Extract the categories from the response
  const parsedContent = JSON.parse(
    content?.trim()?.replace(/^"(.*)"$/, '$1') || '{}'
  );

  return parsedContent;
}
