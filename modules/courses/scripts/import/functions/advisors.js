import Logger from '#utils/logger.js';
import { slugify } from '#utils/strings.js';

import Advisor from '../../../models/Advisor.js';

// import { importImage, getImageUrl } from '../utils/images.js';

async function importAdvisors(entity, providerId, ky, testAdvisor) {
  const results = {
    imported: 0,
    updated: 0,
  };

  try {
    Logger.info('Importing advisors');

    const data = await ky
      .get('api/accounts', {
        searchParams: {
          filter: {
            fields: ['id'],
            where: {
              isDeleted: false,
              ...(testAdvisor && { firstName: testAdvisor }),
            },
          },
        },
      })
      .json();
    const numberOfRecords = data.length;

    if (numberOfRecords === 0) {
      Logger.info('No advisors found to import');
      return;
    }

    const filter = {
      where: {
        isDeleted: false,
        ...(testAdvisor && { firstName: testAdvisor }),
      },
      order: 'createdAt ASC',
      include: ['roles'],
    };

    const limit = 10;
    let skip = 0;

    while (skip < numberOfRecords) {
      const accounts = await ky
        .get('api/accounts', {
          searchParams: {
            filter: {
              ...filter,
              limit,
              skip,
            },
          },
        })
        .json();

      // Filter out the advisors
      const advisors = accounts.reduce((acc, account) => {
        if (account.roles.find((role) => role.name === 'advisor')) {
          acc.push(account);
        }
        return acc;
      }, []);

      // Import the advisors
      for (const advisor of advisors) {
        const result = await importAdvisor(advisor, providerId, entity);
        switch (result.status) {
          case 'new':
            results.imported += 1;
            break;
          case 'updated':
            results.updated += 1;
            break;
          default:
            break;
        }
      }

      // Increase the offset
      skip += limit;
    }

    // if (results.imported + results.updated > 0) {
    Logger.info(`${results.imported} advisors imported`);
    Logger.info(`${results.updated} advisors updated`);
    // }
  } catch (error) {
    Logger.error('Error when trying to import advisor:', error);
  }

  return results;
}

async function importAdvisor(advisor, providerId, entity) {
  const result = {
    status: null,
    record: null,
    error: null,
  };

  const fullName = `${advisor.firstName} ${advisor.lastName}`
    .replace(/undefined/g, '')
    .replace(/\s+/g, ' ')
    .trim();

  Logger.info(`- ${fullName}`);

  try {
    let existingAdvisor = await Advisor.findOne({
      _id: advisor.id,
    });

    const {
      id,
      isEnabled, // enabled?
      //   isDeleted, // deleted?
      createdAt,
      firstName,
      lastName,
      email,
      //   gender,
      //   birthday,
      //   avatar,
      street,
      postalCode,
      city,
      countryCode,
      //   countryId,
      //   regionId, // region?
      phone,
      //   preferences,
      //   lastLoginAt,
      //   lastLoginFrom,
      //   twoFactorEnabled,
      //   absenceMessage,
      //   welcomeMessage,
    } = advisor;

    const baseAdvisor = {
      entity,
      provider: providerId,
      enabled: isEnabled,
      deleted: false,
      gender: '', // TODO: gender
      firstName,
      lastName,
      address: {
        street: street || '',
        additionalAddress: '',
        zip: postalCode || '',
        city: city || '',
        state: '', // TODO: state
        country: countryCode || '', // TODO: countryId?
      },
      email,
      phone,
    };

    // Download avatar
    // const imageUrl = getImageUrl({ image });
    // if (imageUrl && !existingAdvisor?.images?.default) {
    //   const imageFile = await importImage(imageUrl, entity);
    //   if (imageFile) baseCourse.images = { default: imageFile };
    // }

    // Update existing advisor
    if (existingAdvisor) {
      await existingAdvisor.updateOne(baseAdvisor);

      // Set result
      result.status = 'updated';
      result.record = existingAdvisor;
    }

    // Create new advisor
    else {
      // Ensure advisor has a valid slug within the entity
      const slug = await Advisor.getAvailableSlug(slugify(fullName), entity);

      existingAdvisor = await Advisor.create({
        ...baseAdvisor,
        _id: id,
        slug,
        createdAt,
      });

      // Set result
      result.status = 'new';
      result.record = existingAdvisor;
    }
  } catch (error) {
    Logger.error(`Error when trying to import advisor "${fullName}"`, error);
    result.error = error;
  }

  return result;
}

export default importAdvisors;
