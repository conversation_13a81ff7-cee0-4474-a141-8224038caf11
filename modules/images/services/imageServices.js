import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';
import fs from 'fs';
import { extname } from 'path';
import mimeTypes from 'mime-types';
import { once } from 'events';
import { finished } from 'stream';
import { promisify } from 'util';

import { errorCodes, errors, generateError } from '#utils/appError.js';
import {
  encodeImageToBlurhash,
  fileTypes,
  getImageSizeData,
  resizeImage,
} from '#utils/files.js';
import Logger from '#utils/logger.js';
import { randomFilename, uploadFile } from '#utils/storage.js';

const allowedContentTypes = fileTypes.anyImage;

// The default version of stream.finished() is callback-based but can be turned into a Promise-based version via util.promisify()
const streamFinished = promisify(finished);

/**
 * Get the original file name from a URL string
 * @param {String} filename - The original filename from the URL string
 * @returns {String} The original file name
 * @example getOriginalFileName('https://example.com/image.jpg?w=200&h=200') // 'image'
 */
export function getOriginalFileName(filename) {
  return filename.substring(0, filename.lastIndexOf('.'));
}

// Default options for the saveRemoteImage function
const defaultSaveRemoteImageOptions = {
  getOriginalFileNameFn: getOriginalFileName,
  bucket: 'hope-images',
  maxFileSize: '15mb',
};

/**
 * Save a remote image to the CDN
 * @param {Object} params - The parameters object
 * @param {String} params.imageUrl - (required) The URL to the image
 * @param {String} params.entityId - (required) The entity id used to save the image: every entity has a folder in the CDN to save the images, and the folder is named after the entity id
 * @param {String} params.tempFolder - (optional) Custom folder to save the file temporarily before uploading it to the CDN (default: undefined)
 * @param {Function} [params.getOriginalFileNameFn] - (optional) The function to get the original file name from the URL (default: getOriginalFileName)
 * @param {Object} [params.customS3Client] - (optional) The custom S3 client to use (default: null)
 * @param {String} [params.bucket] - (optional) The bucket to save the image to (default: 'hope-images')
 * @param {String} [params.maxFileSize] - (optional) The maximum file size allowed (default: '15mb')
 * @returns {Promise<Object>} The image data
 */
export async function saveRemoteImage(options = defaultSaveRemoteImageOptions) {
  const {
    imageUrl,
    entityId,
    tempFolder,
    getOriginalFileNameFn,
    customS3Client,
    bucket,
    maxFileSize,
    verbose,
  } = {
    ...defaultSaveRemoteImageOptions,
    ...options,
  };

  // Check if the image URL is valid
  const url = new URL(imageUrl);
  const urlString = url.toString();

  if (!urlString) {
    return {
      error: generateError('Invalid image URL', 'INVALID_IMAGE_URL', 400),
    };
  }

  let tempOutput;
  let filePath;
  let error;

  let response;

  try {
    // Create a client for downloading images
    const downloadClient = createExternalApiClient({
      timeout: 60000, // Longer timeout for image downloads
    });

    response = await downloadClient.get(urlString);
  } catch (err) {
    const normalizedError = await handleKyError(
      err,
      `downloading image from ${imageUrl}`
    );
    if (verbose) {
      Logger.error(
        'Error when trying to download the file from this url:',
        imageUrl,
        'Error:',
        normalizedError.message
      );
    }
    return { error: errors.file_download_error(), imageUrl };
  }

  const contentType = response.headers.get('content-type');

  let originalFilename = urlString.split('/').pop().split(/\?|#/).shift();

  // Try to get the mime-type from the file name
  let fileExtension = originalFilename.split('.').pop();
  let fileMime = mimeTypes.lookup(fileExtension);

  // If it cannot be detected, try to obtain from headers
  if (!fileMime && mimeTypes.contentType(contentType)) {
    fileMime = mimeTypes.contentType(contentType).split(';').shift();

    // And detect the file extension from the mime-type
    fileExtension = mimeTypes.extension(fileMime);
  }

  const stream = response.body;
  let isFirstChunk = true;

  if (
    allowedContentTypes.length > 0 &&
    !allowedContentTypes.includes(fileMime)
  ) {
    stream.destroy();

    return {
      error: generateError(
        `File type "${fileMime}" is not allowed. Must be in [${allowedContentTypes.join(
          ', '
        )}].`,
        errorCodes.FILE_TYPE_NOT_ALLOWED,
        400
      ),
    };
  }

  // Generate a random file name
  const filename = randomFilename();

  // This is to reject the download if it exceeds the max file size defined
  for await (const chunk of stream) {
    if (isFirstChunk) {
      isFirstChunk = false;

      try {
        // Use the generated random filename, with the extension provided by fileType
        filePath = `${tempFolder ?? `temp/${bucket}`}/${filename}.${fileExtension}`;

        tempOutput = fs.createWriteStream(filePath);
      } catch (err) {
        Logger.error('Error trying to read file info:', err);
        error = errors.file_download_error();
        stream.destroy();
      }
    }

    if (!error) {
      if (tempOutput) {
        if (!tempOutput.write(Buffer.from(chunk))) {
          // Handle backpressure
          await once(tempOutput, 'drain');
        }
      }

      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        if (stats.size > maxFileSize) {
          // If the file size exceeds the allowed, end the stream
          error = errors.file_too_big();
          stream.destroy();
        }
      }
    }
  }

  if (tempOutput) {
    tempOutput.end();
    // Wait until done. Throws if there are errors.
    await streamFinished(tempOutput);
  }

  if (error) {
    // If an error ocurred delete the file and return the error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    return { error };
  }
  // Get image width and height
  let { width, height } = await getImageSizeData(filePath);

  // Get file size
  let { size } = fs.statSync(filePath);
  const resolution = width * height;

  // If image is bigger than 16.5 megapixels, resize it
  if (resolution > 16500000) {
    // Resizing to fit in 16 megapixels (5333x3000)
    await resizeImage(filePath, filePath, 5333, 3000);

    // Obtain new image size and dimensions
    const { width: newWidth, height: newHeight } =
      await getImageSizeData(filePath);
    width = newWidth;
    height = newHeight;
    // eslint-disable-next-line
    size = fs.statSync(filePath).size;
  }

  // Upload the file
  const uploadInfo = await uploadFile(filePath, {
    bucket,
    key: entityId,
    mime: fileMime,
    customS3Client,
  });

  const blurhash = await encodeImageToBlurhash(filePath);

  // Delete the temp file
  try {
    fs.unlinkSync(filePath);
  } catch (err) {
    Logger.error('Error trying to delete temp file:', err);
  }

  // Calculate remotename and remote url
  const remoteName = uploadInfo.Key.split('/').pop();

  // Try to obtain original filename
  originalFilename = getOriginalFileNameFn(originalFilename);

  if (originalFilename === '')
    originalFilename = remoteName.substring(0, remoteName.lastIndexOf('.'));

  const fileObject = {
    containerId: entityId,
    extension: extname(remoteName),
    name: remoteName,
    originalFilename,
    size,
    mime: fileMime,
    width,
    height,
    blurhash,
  };

  return {
    file: fileObject,
  };
}
