import _ from 'lodash';

import Logger from '#utils/logger.js';
import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';
import { slugify } from '#utils/strings.js';
import { toUnixDate } from '#utils/dates.js';

import Entity from '#modules/entities/models/Entity.js';
import Event from '#modules/events/models/Event.js';
import EventCategory from '#modules/events/models/EventCategory.js';
import EventOrganizer from '#modules/events/models/EventOrganizer.js';
import EventRegion from '#modules/events/models/EventRegion.js';
import EventFood from '#modules/events/models/EventFood.js';
import EventWorkshop from '#modules/events/models/EventWorkshop.js';
import EventPrice from '#modules/events/models/EventPrice.js';
import EventAccommodation from '#modules/events/models/EventAccommodation.js';

import { saveRemoteImage } from '#modules/images/controllers/imageController.js';
import activeEvents from './active-events.js';

const getRegistrationTerms = () => [
  {
    name: 'consent',
    title: 'Einwilligung',
    description: {
      type: 'doc',
      content: [
        {
          type: 'bulletList',
          content: [
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      type: 'text',
                      text: 'Ich willige ein, dass meine personenbezogenen Daten durch den Veranstalter elektronisch gespeichert und zur Durchführung der Veranstaltung verwendet werden dürfen.',
                    },
                  ],
                },
              ],
            },
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      type: 'text',
                      text: 'Ich willige ein, dass Name, Adresse, E-Mail und Telefonnummer in Form einer Teilnahmeliste allen Teilnehmenden zur Verfügung gestellt werden können. Über die Weitergabe der Teilnahmeliste an die Teilnehmenden hinaus, werden die Daten nicht an Dritte weitergegeben, soweit es nicht für die Durchführung der Veranstaltung erforderlich ist.',
                    },
                  ],
                },
              ],
            },
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      type: 'text',
                      text: 'Ich willige ein, dass im Zusammenhang mit der Maßnahme Foto-, Video- oder Tonaufnahmen von mir gemacht werden und diese für die Öffentlichkeitsarbeit des Veranstalters und des Bundesverbandes genutzt werden können. Sollte ein berechtigtes Interesse daran bestehen, dass Foto-, Video- der Tonaufnahmen nicht veröffentlicht werden, muss dies schriftlich vor Beginn der Maßnahme dem Veranstalter mitgeteilt werden.',
                    },
                  ],
                },
              ],
            },
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      type: 'text',
                      text: 'Ich willige ein, Informationen zu weiteren, zukünftigen Maßnahmen des Veranstalters per Post oder E-Mail zu erhalten.',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    required: true,
  },
  {
    name: 'revocation',
    title: 'Widerruf',
    description: {
      type: 'doc',
      content: [
        {
          type: 'bulletList',
          content: [
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      type: 'text',
                      text: 'Ich habe zur Kenntnis genommen, dass ich jederzeit ohne Angabe von Gründen von meinem Widerspruchsrecht Gebrauch machen kann und die erteilte Einwilligung mit Wirkung für die Zukunft abändern oder gänzlich widerrufen kann. Ein Widerruf kann postalisch oder per E-Mail an den Veranstalter übermittelt werden.',
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          type: 'paragraph',
        },
      ],
    },
    required: true,
  },
  {
    name: 'termsAndConditions',
    title: 'Teilnahmebedinungen',
    description: {
      type: 'doc',
      content: [
        {
          type: 'bulletList',
          content: [
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      type: 'text',
                      text: 'Ich habe die Teilnahmebedingungen gelesen und akzeptiere sie.',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    required: true,
  },
];

const importImage = async function (publicUrl, entity) {
  try {
    // Upload the file to the CDN
    const imageFile = await saveRemoteImage(publicUrl, entity);
    Logger.info('Imported image from', publicUrl);
    return imageFile;
  } catch (error) {
    Logger.error(`Error when trying to obtain image from ${publicUrl}`, error);
  }
};

const importEventCategories = async function (ky, entity, lastSync = 0) {
  try {
    Logger.info('Importing event categories');

    const params = {
      type: 2000,
      table: 'tx_amseventmanagerwave_domain_model_eventcategory',
      sorting: 'tstamp ASC',
      hidden: 1,
      filter: {
        tstamp: { operator: '>=', value: lastSync },
        // CONDITION: 'AND',
        // title: {
        //   operator: '=',
        //   value: 'Bibelschule',
        // },
        // 'CONDITION-1': 'AND',
        // uid: {
        //   operator: '=',
        //   value: 23919,
        // },
      },
    };

    const categoryCount = await ky
      .get('', {
        searchParams: { ...params, count: 1 },
      })
      .json();

    let importCount = 0;
    let updateCount = 0;
    const limit = 10;
    let offset = 0;

    const { numberOfRecords } = categoryCount;

    if (numberOfRecords > 0) {
      Logger.info(`Attempting to import ${numberOfRecords} categories`);

      while (offset < numberOfRecords) {
        Logger.info(
          `Checking categories ${offset + 1} to ${Math.min(
            limit + offset,
            numberOfRecords
          )} of ${numberOfRecords}\n`
        );

        const categories = await ky
          .get('', {
            searchParams: { ...params, limit: `${offset},${limit}` },
          })
          .json();

        for (const category of categories) {
          if (_.isEmpty(category.title)) {
            Logger.warning(
              `Not importing category with UID ${category.uid} because its title is empty!`
            );
            continue;
          }

          let existingCategory = await EventCategory.findOne({
            entity,
            importID: {
              type: 'waveware',
              recordID: category.waveware_id,
            },
            deleted: false,
          });

          const {
            hidden, // -> hidden
            deleted, // -> deleted
            title, // -> title
            waveware_id, // -> importID
          } = category;

          const baseCategory = {
            enabled: !hidden,
            deleted,
            title,
            importID: {
              type: 'waveware',
              recordID: waveware_id,
            },
          };

          if (existingCategory) {
            Logger.info(
              `Updating category with external UID ${category.uid} and local ID ${existingCategory.id}`
            );

            // Update the existing category
            await existingCategory.updateOne(baseCategory);

            updateCount += 1;
            Logger.success(
              `Successfully updated category. ID: ${existingCategory.id}`
            );
          } else {
            Logger.info(`Importing category with UID ${category.uid}`);

            // Generate slug
            let slug = slugify(category.title);

            // Ensure category has a valid slug within its siblings
            slug = await EventCategory.getAvailableSlug(slug, entity);

            // Create the new category
            existingCategory = await EventCategory.create({
              ...baseCategory,
              slug,
              createdAt: new Date(),
              updatedAt: new Date(),
              entity,
            });

            importCount += 1;
            Logger.success(
              `Successfully imported category. ID: ${existingCategory.id}`
            );
          }
        }

        // Increase the offset
        offset += limit;
      }
    }

    if (importCount === 0 && updateCount === 0) {
      Logger.info('No categories found to import');
    } else {
      Logger.info(`${importCount} categories imported`);
      Logger.info(`${updateCount} categories updated`);
    }
  } catch (error) {
    Logger.error('Error when trying to import categories:', error);
  }
};

const importEventOrganizers = async function (ky, entity, lastSync = 0) {
  try {
    Logger.info('Importing event organizers');

    const params = {
      type: 2000,
      table: 'tx_amseventmanagerwave_domain_model_organizer',
      sorting: 'tstamp ASC',
      hidden: 1,
      filter: {
        tstamp: { operator: '>=', value: lastSync },
      },
    };

    const organizerCount = await ky
      .get('', {
        searchParams: { ...params, count: 1 },
      })
      .json();

    let importCount = 0;
    let updateCount = 0;
    const limit = 10;
    let offset = 0;

    const { numberOfRecords } = organizerCount;

    if (numberOfRecords > 0) {
      Logger.info(`Attempting to import ${numberOfRecords} organizers`);

      while (offset < numberOfRecords) {
        Logger.info(
          `Checking organizers ${offset + 1} to ${Math.min(
            limit + offset,
            numberOfRecords
          )} of ${numberOfRecords}\n`
        );

        const organizers = await ky
          .get('', {
            searchParams: { ...params, limit: `${offset},${limit}` },
          })
          .json();

        for (const organizer of organizers) {
          if (_.isEmpty(organizer.title)) {
            Logger.warning(
              `Not importing organizer with UID ${organizer.uid} because its title is empty!`
            );
            continue;
          }

          let existingOrganizer = await EventOrganizer.findOne({
            importID: {
              type: 'waveware',
              recordID: organizer.waveware_id,
            },
            deleted: false,
          });

          const {
            hidden, // -> enabled
            deleted, // -> deleted
            title, // -> title
            acronym, // -> acronym
            link, // -> website
            waveware_id, // -> importID
          } = organizer;

          const baseOrganizer = {
            enabled: !hidden,
            deleted,
            title,
            acronym,
            website: link
              .replace('http://', '')
              .replace('https://', '')
              .replace(/\/$/, ''), // removing trailing slash
            importID: {
              type: 'waveware',
              recordID: waveware_id,
            },
          };

          if (existingOrganizer) {
            Logger.info(
              `Updating organizer with external UID ${organizer.uid} and local ID ${existingOrganizer.id}`
            );

            // Update the existing organizer
            await existingOrganizer.updateOne(baseOrganizer);

            updateCount += 1;
            Logger.success(
              `Successfully updated organizer. ID: ${existingOrganizer.id}`
            );
          } else {
            Logger.info(`Importing organizer with UID ${organizer.uid}`);

            // Create the new organizer
            existingOrganizer = await EventOrganizer.create({
              ...baseOrganizer,
              createdAt: new Date(),
              updatedAt: new Date(),
              entity,
            });

            importCount += 1;
            Logger.success(
              `Successfully imported organizer. ID: ${existingOrganizer.id}`
            );
          }
        }

        // Increase the offset
        offset += limit;
      }
    }

    if (importCount === 0 && updateCount === 0) {
      Logger.info('No organizers found to import');
    } else {
      Logger.info(`${importCount} organizers imported`);
      Logger.info(`${updateCount} organizers updated`);
    }
  } catch (error) {
    Logger.error('Error when trying to import organizers:', error);
  }
};

const importEventRegions = async function (ky, entity, lastSync = 0) {
  try {
    Logger.info('Importing event regions');

    const params = {
      type: 2000,
      table: 'tx_amseventmanagerwave_domain_model_region',
      sorting: 'tstamp ASC',
      filter: {
        tstamp: { operator: '>=', value: lastSync },
      },
    };

    const regionCount = await ky
      .get('', {
        searchParams: { ...params, count: 1 },
      })
      .json();

    let importCount = 0;
    let updateCount = 0;
    const limit = 10;
    let offset = 0;

    const { numberOfRecords } = regionCount;

    if (numberOfRecords > 0) {
      Logger.info(`Attempting to import ${numberOfRecords} regions`);

      while (offset < numberOfRecords) {
        Logger.info(
          `Checking regions ${offset + 1} to ${Math.min(
            limit + offset,
            numberOfRecords
          )} of ${numberOfRecords}\n`
        );

        const regions = await ky
          .get('', {
            searchParams: { ...params, limit: `${offset},${limit}` },
          })
          .json();

        for (const region of regions) {
          if (_.isEmpty(region.title)) {
            Logger.warning(
              `Not importing region with UID ${region.uid} because its title is empty!`
            );
            continue;
          }

          let existingRegion = await EventRegion.findOne({
            entity,
            importID: {
              type: 'waveware',
              recordID: region.waveware_id,
            },
            deleted: false,
          });

          const {
            deleted, // -> deleted
            title, // -> title
            waveware_id, // -> importID
          } = region;

          const baseRegion = {
            enabled: true,
            deleted,
            title,
            importID: {
              type: 'waveware',
              recordID: waveware_id,
            },
          };

          if (existingRegion) {
            Logger.info(
              `Updating region with external UID ${region.uid} and local ID ${existingRegion.id}`
            );

            // Update the existing region
            await existingRegion.updateOne(baseRegion);

            updateCount += 1;
            Logger.success(
              `Successfully updated region. ID: ${existingRegion.id}`
            );
          } else {
            Logger.info(`Importing region with UID ${region.uid}`);

            // Generate slug
            let slug = slugify(region.title);

            // Ensure region has a valid slug within its siblings
            slug = await EventRegion.getAvailableSlug(slug, entity);

            // Create the new region
            existingRegion = await EventRegion.create({
              ...baseRegion,
              slug,
              createdAt: new Date(),
              updatedAt: new Date(),
              entity,
            });

            importCount += 1;
            Logger.success(
              `Successfully imported region. ID: ${existingRegion.id}`
            );
          }
        }

        // Increase the offset
        offset += limit;
      }
    }

    if (importCount === 0 && updateCount === 0) {
      Logger.info('No regions found to import');
    } else {
      Logger.info(`${importCount} regions imported`);
      Logger.info(`${updateCount} regions updated`);
    }
  } catch (error) {
    Logger.error('Error when trying to import regions:', error);
  }
};

const importEvents = async function (ky, entity, lastSync = 0) {
  let importCount = 0;
  let updateCount = 0;
  let deleteCount = 0;

  try {
    Logger.info('Importing events');

    const params = {
      type: 2000,
      table: 'tx_amseventmanagerwave_domain_model_event',
      sorting: 'waveware_id ASC, deleted DESC, tstamp ASC',
      related: 1,
      hidden: 1,
      deleted: 1,
      filter: {
        tstamp: { operator: '>=', value: lastSync },
        // CONDITION: 'AND',
        // waveware_id: {
        //   operator: '=',
        //   value: 'VE006096',
        // },
        // title: {
        //   operator: '=',
        //   value: 'CPA- und Jugenleiter/-innen-Schulung',
        //   // value: 'Neujahrsfreizeit Schwarzenberg',
        //   // value: 'Teens-Silvestertage 2022/2023',
        //   // value: 'Fußballturnier BW 2023',
        // },
      },
    };

    const eventCount = await ky
      .get('', {
        searchParams: { ...params, count: 1 },
      })
      .json();

    const limit = 10;
    let offset = 0;

    const { numberOfRecords } = eventCount;

    if (numberOfRecords > 0) {
      Logger.info(`Attempting to import ${numberOfRecords} events`);

      while (offset < numberOfRecords) {
        Logger.info(
          `Checking events ${offset + 1} to ${Math.min(
            limit + offset,
            numberOfRecords
          )} of ${numberOfRecords}\n`
        );

        const events = await ky
          .get('', {
            searchParams: { ...params, limit: `${offset},${limit}` },
          })
          .json();

        let i = -1;
        for (const event of events) {
          i += 1;
          if (_.isEmpty(event.title)) {
            Logger.warning(
              `Not importing event with UID ${event.uid} because its title is empty!`
            );
            continue;
          }

          // Sometimes there are multiple events with the same waveware_id that are being imported.
          // Continue if the next event is the same
          const nextEvent = events[i + 1];
          if (nextEvent?.waveware_id === event.waveware_id) {
            continue;
          }

          const existingEvent = await Event.findOne({
            entity,
            importID: {
              type: 'waveware',
              recordID: event.waveware_id,
            },
            deleted: false,
          });

          if (event.deleted) {
            Logger.info('TRYING TO DELETE EVENT');
            if (existingEvent) {
              Logger.info(
                `Deleting event with external UID ${event.uid} and local ID ${existingEvent.id}`
              );
              await existingEvent.updateOne({ deleted: true });

              deleteCount += 1;
              Logger.success(
                `Successfully deleted event. ID: ${existingEvent.id}`
              );
            }
            continue;
          }

          if (existingEvent) {
            Logger.info(
              `Updating event with external UID ${event.uid} and local ID ${existingEvent.id}`
            );
          } else {
            Logger.info(`Importing event with UID ${event.uid}`);
          }

          const {
            // hidden, // -> enabled
            // deleted, // -> deleted
            title, // -> title
            waveware_id, // -> importID
            image, // -> image
            start_date, // -> startsAt
            end_date, // -> endsAt
            status, // -> onlineRegistration
            event_location, // -> location
            description, // -> description
            // hint, // -> ???
            // keyword, // -> keywords
            // hint_url, // -> ???
            // contact, // -> ???
            min_participants, // -> minParticipants
            max_participants, // -> maxParticipants
            count_participants, // -> countParticipants
            free_places, // -> fewPlacesThreshold
            audience, // -> audience
            theme, // -> theme
            age_min, // -> ageMin
            age_max, // -> ageMax
            training_sessions, // -> trainingSessions
            deadline, // -> deadline
            early_booking_deadline, // -> earlyBookingDeadline
            info_name, // -> infoName
            info_email, // -> infoEmail
            category, // -> category
            regions, // -> regions
            foods, // -> foods
            price_categories, // prices
            housings, // accommodations
            workshops, // -> workshops
            organizer, // -> organizer
            // EventOrganizer fields
            // street, // -> street
            // organizer_location, // -> zip + location (e.g. "70174 Stuttgart")
            email, // -> email
            phone, // -> phone
            fax, // -> fax
            iban, // -> bank.iban
            bic, // -> bank.bic
            bank, // -> bank.name
          } = event;

          const baseEvent = {
            deleted: false,
            title,
            startsAt: start_date,
            endsAt: end_date,
            onlineRegistration: status === 'Anmeldung', // option: 'Eventkalender'
            ageOfConsent: 18,
            location: event_location,
            description: description,
            // hint... // TODO: Not body. "Hinweise". note,remark,comment,hint
            // hint_url... // TODO: Link at the end of the hint text
            // keyword... // TODO: Not keywords. "Verwendungszweck". purposeOfPayment ??
            // contact... // TODO: Where in frontend?
            minParticipants: min_participants,
            maxParticipants: max_participants,
            countParticipants: count_participants,
            fewPlacesThreshold: free_places,
            audience: audience,
            theme: theme,
            ageMin: age_min,
            ageMax: age_max,
            trainingSessions: training_sessions,
            deadline: !deadline.startsWith('1901') ? deadline : null,
            earlyBookingDeadline: !early_booking_deadline.startsWith('1901')
              ? early_booking_deadline
              : null,
            infoName: info_name,
            infoEmail: info_email,
            additionalFields: [],
            regions: [],
            foods: [],
            prices: [],
            accommodations: [],
            workshops: [],
            registrationTerms: getRegistrationTerms(),
            importID: {
              type: 'waveware',
              recordID: waveware_id,
            },
          };

          if (category) {
            const existingCategory = await EventCategory.findOne({
              entity,
              importID: {
                type: 'waveware',
                recordID: category.waveware_id,
              },
              deleted: false,
            });
            if (existingCategory) {
              baseEvent.category = existingCategory.id;
            }
          }
          if (!baseEvent.category) {
            Logger.warning(
              `Missing category with UID ${category.uid} (${category.title}): Event not imported!`
            );
            continue;
          }

          if (organizer) {
            const existingOrganizer = await EventOrganizer.findOne({
              entity,
              importID: {
                type: 'waveware',
                recordID: organizer.waveware_id,
              },
              deleted: false,
            });

            if (existingOrganizer) {
              /*
              const locationParts = organizer_location.split(' ');
              if (locationParts.length > 1) {
                existingOrganizer.postalCode = locationParts[0];
                existingOrganizer.city = locationParts[1];
              }
              existingOrganizer.street = street,
              existingOrganizer.email = email,
              existingOrganizer.phone = phone,
              existingOrganizer.fax = fax,
              existingOrganizer.bank = {
                name: bank,
                iban: iban,
                bic: bic,
              };
              */
              baseEvent.organizer = existingOrganizer.id;

              // Add organizer info (event overrides)
              baseEvent.organizerEmail = email;
              baseEvent.organizerPhone = phone;
              baseEvent.organizerFax = fax;
              baseEvent.organizerBank = {
                name: bank,
                iban: iban,
                bic: bic,
              };
            }
          }
          if (!baseEvent.organizer) {
            Logger.warning(
              `Missing organizer with UID ${organizer.uid} (${organizer.title}): Event not imported!`
            );
            continue;
          }

          if (!_.isEmpty(regions)) {
            for (const region of regions) {
              const existingRegion = await EventRegion.findOne({
                entity,
                importID: {
                  type: 'waveware',
                  recordID: region.waveware_id,
                },
                deleted: false,
              });
              if (existingRegion) {
                baseEvent.regions = _.uniqBy(
                  [...baseEvent.regions, existingRegion.id],
                  (id) => id.toString()
                );
              }
            }
          }

          for (let j = 1; j <= 12; j += 1) {
            const additionalFieldLabel = event[`additional${j}label`];
            const additionalFieldRequired = event[`additional${j}required`];
            const additionalFieldName = additionalFieldLabel.replace(
              /[^a-zA-Z0-9]/g,
              ''
            );

            if (additionalFieldLabel) {
              baseEvent.additionalFields.push({
                name: additionalFieldName,
                label: additionalFieldLabel,
                required: additionalFieldRequired,
                type: 'Input',
                typeOption: 'text',
                size: 'lg',
              });
            }
          }

          // Functions to generate inline data (called once the event was created)
          const generateFoods = async (_event, _entity) => {
            const _foods = [];
            const _wavewareIds = [];

            for (const food of foods) {
              const existingFood = await EventFood.findOne({
                entity: _entity,
                event: _event,
                importID: {
                  type: 'waveware',
                  recordID: food.waveware_id, // -> importID
                },
                deleted: false,
              });

              if (food.deleted) {
                if (existingFood) {
                  await existingFood.updateOne({ deleted: true });
                }
                continue;
              }

              const baseFood = {
                enabled: true,
                deleted: false,
                title: food.title,
                price: 0, // does not exist in waveware
                importID: {
                  type: 'waveware',
                  recordID: food.waveware_id,
                },
              };

              if (existingFood) {
                await existingFood.updateOne(baseFood);

                Logger.success(
                  `Successfully updated food. ID: ${existingFood.id}`
                );

                _foods.push(existingFood._id);
              } else {
                // Create the new food
                const newFood = await EventFood.create({
                  ...baseFood,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  entity: _entity,
                  event: _event,
                });

                Logger.success(`Successfully imported food. ID: ${newFood.id}`);

                _foods.push(newFood._id);
              }
              _wavewareIds.push(baseFood.importID.recordID);
            }

            // Cleanup foods that were removed from waveware
            await EventFood.updateMany(
              {
                entity: _entity,
                event: _event,
                importID: {
                  type: 'waveware',
                  recordID: { $nin: _wavewareIds },
                },
              },
              {
                deleted: true,
              }
            );

            return _foods;
          };

          const generatePrices = async (_event, _entity) => {
            const _prices = [];
            const _wavewareIds = [];

            for (const _price of price_categories) {
              const {
                deleted, // -> deleted
                commission, // -> commission
                price, // -> price
                price_category /*
                  - title -> title
                  - early_booking -> earlyBooking
                  - visible
                  - deleted
                  - waveware_id -> importID
                */,
              } = _price;

              const existingPrice = await EventPrice.findOne({
                entity: _entity,
                event: _event,
                importID: {
                  type: 'waveware',
                  recordID: price_category.waveware_id,
                },
                deleted: false,
              });

              if (deleted) {
                if (existingPrice) {
                  await existingPrice.updateOne({ deleted: true });
                }
                continue;
              }

              const basePrice = {
                enabled: true,
                deleted: false,
                title: price_category.title,
                price,
                commission,
                earlyBooking: price_category.early_booking,
                importID: {
                  type: 'waveware',
                  recordID: price_category.waveware_id,
                },
              };

              if (existingPrice) {
                await existingPrice.updateOne(basePrice);

                Logger.success(
                  `Successfully updated price. ID: ${existingPrice.id}`
                );

                _prices.push(existingPrice._id);
              } else {
                // Create the new price
                const newPrice = await EventPrice.create({
                  ...basePrice,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  entity: _entity,
                  event: _event,
                });

                Logger.success(
                  `Successfully imported price. ID: ${newPrice.id}`
                );

                _prices.push(newPrice._id);
              }
              _wavewareIds.push(basePrice.importID.recordID);
            }

            // Cleanup prices that were removed from waveware
            await EventPrice.updateMany(
              {
                entity: _entity,
                event: _event,
                importID: {
                  type: 'waveware',
                  recordID: { $nin: _wavewareIds },
                },
              },
              {
                deleted: true,
              }
            );

            return _prices;
          };

          const generateAccommodations = async (_event, _entity) => {
            const _accommodations = [];
            const _wavewareIds = [];

            for (const _housing of housings) {
              const {
                deleted, // -> deleted
                price, // -> price
                housing /*
                  - title -> title
                  - deleted
                  - waveware_id -> importID
                  - type
                    - title
                    - visible
                    - deleted
                    - waveware_id
                */,
              } = _housing;

              const existingAccommodation = await EventAccommodation.findOne({
                entity: _entity,
                event: _event,
                importID: {
                  type: 'waveware',
                  recordID: housing.waveware_id,
                },
                deleted: false,
              });

              if (deleted) {
                if (existingAccommodation) {
                  await existingAccommodation.updateOne({ deleted: true });
                }
                continue;
              }

              const baseAccommodation = {
                enabled: true,
                deleted: false,
                title: housing.title,
                price: price,
                importID: {
                  type: 'waveware',
                  recordID: housing.waveware_id,
                },
              };

              if (existingAccommodation) {
                await existingAccommodation.updateOne(baseAccommodation);

                Logger.success(
                  `Successfully updated accommodation. ID: ${existingAccommodation.id}`
                );

                _accommodations.push(existingAccommodation._id);
              } else {
                // Create the new accommodation
                const newAccommodation = await EventAccommodation.create({
                  ...baseAccommodation,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  entity: _entity,
                  event: _event,
                });

                Logger.success(
                  `Successfully imported accommodation. ID: ${newAccommodation.id}`
                );

                _accommodations.push(newAccommodation._id);
              }
              _wavewareIds.push(baseAccommodation.importID.recordID);
            }

            // Cleanup accommodations that were removed from waveware
            await EventAccommodation.updateMany(
              {
                entity: _entity,
                event: _event,
                importID: {
                  type: 'waveware',
                  recordID: { $nin: _wavewareIds },
                },
              },
              {
                deleted: true,
              }
            );

            return _accommodations;
          };

          const generateWorkshops = async (_event, _entity) => {
            const _workshops = [];
            const _wavewareIds = [];

            for (const _workshop of workshops) {
              const {
                deleted, // -> deleted
                // status, // -> not used (always returns 'Anmeldung')
                additional_service, // -> additionalService
                contributor, // -> contributor
                additional_info, // -> description (if workshop.description doesn't exist)
                // max_participants, // -> maxParticipants
                // count_participants, // -> countParticipants
                // free_places, // -> not used (count_participants used instead)
                price, // -> price
                workshop /*
                  - title -> title
                  - description ->
                  - deleted
                  - waveware_id -> importID
                */,
              } = _workshop;

              const existingWorkshop = await EventWorkshop.findOne({
                entity: _entity,
                event: _event,
                importID: {
                  type: 'waveware',
                  recordID: workshop.waveware_id,
                },
                deleted: false,
              });

              if (deleted) {
                if (existingWorkshop) {
                  await existingWorkshop.updateOne({ deleted: true });
                }
                continue;
              }

              const baseWorkshop = {
                enabled: true,
                deleted: false,
                title: workshop.title,
                description: workshop.description || additional_info,
                price,
                contributor,
                additionalService: additional_service,
                minParticipants: 0,
                maxParticipants: _workshop.max_participants,
                countParticipants: _workshop.count_participants,
                importID: {
                  type: 'waveware',
                  recordID: workshop.waveware_id,
                },
              };

              if (existingWorkshop) {
                await existingWorkshop.updateOne(baseWorkshop);

                Logger.success(
                  `Successfully updated workshop. ID: ${existingWorkshop.id}`
                );

                _workshops.push(existingWorkshop._id);
              } else {
                // Create the new workshop
                const newWorkshop = await EventWorkshop.create({
                  ...baseWorkshop,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  entity: _entity,
                  event: _event,
                });

                Logger.success(
                  `Successfully imported workshop. ID: ${newWorkshop.id}`
                );

                _workshops.push(newWorkshop._id);
              }
              _wavewareIds.push(baseWorkshop.importID.recordID);
            }

            // Cleanup workshops that were removed from waveware
            await EventWorkshop.updateMany(
              {
                entity: _entity,
                event: _event,
                importID: {
                  type: 'waveware',
                  recordID: { $nin: _wavewareIds },
                },
              },
              {
                deleted: true,
              }
            );

            return _workshops;
          };

          // Download image (new event or if event didn't have an image yet)
          if (
            image &&
            image.publicUrl !== '' &&
            (!existingEvent || !existingEvent.image?.file)
          ) {
            const isPublicImage = !image.publicUrl.includes(
              'index.php?eID=dumpFile'
            );
            if (isPublicImage) {
              const imageFile = await importImage(image.publicUrl, entity);
              if (imageFile) {
                baseEvent.image = {
                  caption: title,
                  file: imageFile,
                };
              }
            } else {
              Logger.warning(
                `Non-public file: Could not download image of "${title}" (uid: ${event.uid})`
              );
            }
          }

          if (existingEvent) {
            // Update relations
            baseEvent.foods = await generateFoods(existingEvent, entity);
            baseEvent.prices = await generatePrices(existingEvent, entity);
            baseEvent.accommodations = await generateAccommodations(
              existingEvent,
              entity
            );
            baseEvent.workshops = await generateWorkshops(
              existingEvent,
              entity
            );

            // Update the existing event
            await existingEvent.updateOne(baseEvent);

            updateCount += 1;
            Logger.success(
              `Successfully updated event. ID: ${existingEvent.id} (${existingEvent.title})`
            );
          } else {
            // Generate slug
            let slug = slugify(event.title);

            // Ensure event has a valid slug within its siblings
            slug = await Event.getAvailableSlug(slug, entity);

            // Create the new event
            const newEvent = await Event.create({
              ...baseEvent,
              enabled: !!activeEvents.find((e) => e.wavewareID === waveware_id), // TODO: false
              deleted: false,
              slug,
              createdAt: new Date(),
              updatedAt: new Date(),
              entity,
            });

            // Add relations
            newEvent.foods = await generateFoods(newEvent, entity);
            newEvent.prices = await generatePrices(newEvent, entity);
            newEvent.accommodations = await generateAccommodations(
              newEvent,
              entity
            );
            newEvent.workshops = await generateWorkshops(newEvent, entity);
            await newEvent.save();

            importCount += 1;
            Logger.success(
              `Successfully imported event. ID: ${newEvent.id} (${newEvent.title})`
            );
          }
        }

        // Increase the offset
        offset += limit;
      }
    }

    if (importCount === 0 && updateCount === 0) {
      Logger.info('No events found to import');
    } else {
      Logger.info(`${importCount} events imported`);
      Logger.info(`${updateCount} events updated`);
      Logger.info(`${deleteCount} events deleted`);
    }
  } catch (error) {
    Logger.error('Error when trying to import events:', error);
  }
  return {
    imported: importCount,
    updated: updateCount,
    deleted: deleteCount,
  };
};

const importWavewareData = async function (entity, lastSync = 0) {
  const { WAVEWARE_API_URL, WAVEWARE_API_SECRET } = process.env;
  if (!WAVEWARE_API_URL || !WAVEWARE_API_SECRET) {
    return { error: 'Waveware API configuration missing' };
  }

  const client = createExternalApiClient({
    prefixUrl: WAVEWARE_API_URL,
    headers: { secret: WAVEWARE_API_SECRET },
  });

  try {
    await importEventCategories(client, entity, lastSync);
    await importEventOrganizers(client, entity, lastSync);
    await importEventRegions(client, entity, lastSync);
    const eventResults = await importEvents(client, entity, lastSync);
    return eventResults;
  } catch (error) {
    const normalizedError = await handleKyError(error, 'waveware-import');
    Logger.error('Waveware import failed:', normalizedError);
    throw normalizedError;
  }
};

export default async function runEventsWavewareImport(task) {
  const { entity: entityId } = task;

  const entity = await Entity.findById(entityId, 'id name');

  const lastSync = task.lastSync ? toUnixDate(task.lastSync) : 0;
  // const lastSync = 0; // Full import...

  const results = await importWavewareData(entity, lastSync);

  return results;
}
