# HTTP Client Migration: From Axios to Ky

## Overview

This document outlines the migration from Axios to Ky as the primary HTTP client library. The migration was implemented to provide better modern JavaScript support, improved error handling, and a more lightweight solution for HTTP requests.

## Why Ky?

### Advantages of Ky over Axios

1. **Modern JavaScript**: Built with modern JavaScript features and ES modules
2. **Fetch-based**: Built on top of the native Fetch API
3. **Better Error Handling**: More consistent and predictable error handling
4. **Retry Logic**: Built-in retry functionality with exponential backoff
5. **Request/Response Hooks**: Powerful hook system for request/response interception

### Key Differences from Axios

| Feature | Axios | Ky |
|---------|-------|-----|
| Base Technology | XMLHttpRequest | Fetch API |
| Retry Logic | Plugin required | Built-in |
| Error Handling | Inconsistent | Standardized |
| Modern JS | Partial | Full ES2017+ |

## Migration Implementation

### 1. Centralized HTTP Client Utilities

The migration introduced standardized HTTP client utilities instead of direct library imports:

#### Core Files
- `utils/kyClient.js` - Main client factory
- `utils/kyErrorHandler.js` - Centralized error handling
- `utils/kyClient.test.js` - Test suite

#### Key Functions

**`createExternalApiClient(options)`**
```javascript
import { createExternalApiClient } from '#utils/kyClient.js';

const client = createExternalApiClient({
  baseUrl: 'https://api.example.com',
  timeout: 30000,
  headers: { 'Authorization': 'Bearer token' },
  enableLogging: true
});
```

**`handleKyError(error, context)`**
```javascript
import { handleKyError } from '#utils/kyErrorHandler.js';

try {
  const response = await client.get('/data');
} catch (error) {
  const normalizedError = await handleKyError(error, 'fetching user data');
  throw normalizedError;
}
```

### 2. Default Configuration

The migration established sensible defaults for external API calls:

```javascript
const DEFAULT_EXTERNAL_RETRY = {
  limit: 3,
  methods: ['get', 'post', 'put', 'patch', 'delete'],
  statusCodes: [408, 413, 429, 500, 502, 503, 504],
  backoffLimit: 30000, // 30 seconds maximum delay
};
```

### 3. Error Handling Standardization

The new error handling system provides:

- **Consistent Error Format**: All errors follow the same structure
- **HTTP Error Detection**: Automatic detection of ky.HTTPError instances
- **Response Parsing**: Attempts JSON parsing, falls back to text
- **Context Information**: Additional context for debugging
- **Structured Logging**: Consistent error logging format

#### Error Object Structure
```javascript
{
  status: 500,
  code: 'HTTP_500',
  message: 'Internal Server Error',
  data: { /* response data */ },
  context: 'fetching user data'
}
```

## Usage Patterns

### 1. Basic HTTP Requests

```javascript
import { createExternalApiClient } from '#utils/kyClient.js';
import { handleKyError } from '#utils/kyErrorHandler.js';

const client = createExternalApiClient({
  prefixUrl: 'https://api.example.com'
});

try {
  // GET request
  const data = await client.get('users').json();

  // POST request
  const result = await client.post('users', {
    json: { name: 'John', email: '<EMAIL>' }
  }).json();

} catch (error) {
  const normalizedError = await handleKyError(error, 'user operations');
  throw normalizedError;
}
```

### 2. File Downloads

```javascript
const downloadClient = createExternalApiClient({
  timeout: 60000, // Longer timeout for file downloads
});

const response = await downloadClient.get(url);
// Handle response stream...
```

### 3. Authenticated Requests

```javascript
const client = createExternalApiClient({
  prefixUrl: apiUrl,
  headers: {
    'Accept': 'application/json',
    'ClientToken': apiToken,
    'Origin': origin
  }
});
```

## Migration Checklist

### ✅ Completed
- [x] Installed ky dependency (`ky: ^1.8.2`)
- [x] Created centralized client utilities (`utils/kyClient.js`)
- [x] Implemented standardized error handling (`utils/kyErrorHandler.js`)
- [x] Added comprehensive test suite (`utils/kyClient.test.js`)
- [x] Updated all external API calls to use new utilities
- [x] Migrated import scripts and services

### 📍 Current State
- All external HTTP requests now use the centralized ky utilities
- No direct ky imports in application code
- Consistent error handling across all HTTP operations
- Comprehensive retry logic with exponential backoff
- Logging and debugging capabilities built-in

## Files Updated

### Core Utilities
- `utils/kyClient.js` - Main client factory
- `utils/kyErrorHandler.js` - Error handling
- `utils/kyClient.test.js` - Test suite

### Services and Controllers
- `modules/images/controllers/imageController.js`
- `modules/images/services/imageServices.js`
- `modules/documents/controllers/documentController.js`
- `modules/ai/services/aiServices.js`
- `modules/articles/services/wordPressServices.js`
- `utils/storage.js`

### Import Scripts
- `scripts/import/typo3-import.js`
- `modules/courses/scripts/import/courses-import.js`
- `modules/articles/tasks/acl-import/helpers/client.js`

## Best Practices

### 1. Always Use Centralized Utilities
```javascript
// ✅ Good
import { createExternalApiClient } from '#utils/kyClient.js';

// ❌ Avoid
import ky from 'ky';
```

### 2. Implement Proper Error Handling
```javascript
// ✅ Good
try {
  const response = await client.get('/api/data');
} catch (error) {
  const normalizedError = await handleKyError(error, 'fetching data');
  throw normalizedError;
}

// ❌ Avoid
const response = await client.get('/api/data'); // No error handling
```

### 3. Configure Appropriate Timeouts
```javascript
// ✅ Good - Different timeouts for different use cases
const apiClient = createExternalApiClient({ timeout: 30000 });
const downloadClient = createExternalApiClient({ timeout: 60000 });

// ❌ Avoid - Using default timeout for all operations
const client = createExternalApiClient();
```

### 4. Use Logging for Debugging
```javascript
// ✅ Good - Enable logging in development
const client = createExternalApiClient({
  enableLogging: process.env.NODE_ENV === 'development'
});
```

## Legacy Code

### Remaining Fetch Usage
Some legacy code still uses the native `fetch` API directly:
- `modules/articles/scripts/modx-import/helpers/modx.js`
- `utils/fetchClient.js` (custom fetch wrapper)

These should be evaluated for migration to the centralized ky utilities in future updates.

## Conclusion

The migration to Ky has provided:
- **Improved Developer Experience**: Consistent API across all HTTP operations
- **Better Error Handling**: Standardized error format and handling
- **Enhanced Reliability**: Built-in retry logic and timeout management
- **Reduced Bundle Size**: Smaller footprint compared to Axios
- **Future-Proof**: Modern JavaScript features and better TypeScript support

The centralized approach ensures consistency and maintainability while providing flexibility for different use cases across the application.
